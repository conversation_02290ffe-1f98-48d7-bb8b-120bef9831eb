# 配置模板使用示例

## 🚀 快速开始

### 1. 最简单的测试
```bash
# 使用最小化配置进行快速测试
python run_simulation.py PhoElec/config_templates/minimal_config.json

# 使用快速测试配置
python run_simulation.py PhoElec/config_templates/quick_test_config.json
```

### 2. 基础功能验证
```bash
# 使用基础配置验证系统功能
python run_simulation.py PhoElec/config_templates/basic_config.json
```

### 3. 完整功能演示
```bash
# 使用完整配置展示所有功能
python run_simulation.py PhoElec/config_templates/complete_config.json
```

## 📋 按设备类型使用

### 红外目标仿真
```bash
# 专门测试红外目标设备
python run_simulation.py PhoElec/config_templates/infrared_target_config.json

# 输出：红外图像、温度分布数据、探测参数
```

### 激光目标仿真
```bash
# 专门测试激光目标设备
python run_simulation.py PhoElec/config_templates/laser_target_config.json

# 输出：激光功率分布、光束参数、探测数据
```

### 电视目标仿真
```bash
# 专门测试可见光目标设备
python run_simulation.py PhoElec/config_templates/tv_target_config.json

# 输出：可见光图像、传感器参数、光学数据
```

### 干扰设备仿真
```bash
# 专门测试各种干扰设备
python run_simulation.py PhoElec/config_templates/jammer_config.json

# 输出：干扰效果图像、功耗数据、覆盖范围分析
```

### 侦察设备仿真
```bash
# 专门测试侦察设备
python run_simulation.py PhoElec/config_templates/recon_config.json

# 输出：侦察数据、目标识别结果、跟踪参数
```

## 🌤️ 按环境场景使用

### 晴朗天气场景
```bash
# 理想天气条件下的仿真
python run_simulation.py PhoElec/config_templates/clear_weather_config.json

# 特点：高能见度、低大气衰减、最佳性能
```

### 恶劣天气场景
```bash
# 雾、雨等恶劣天气条件
python run_simulation.py PhoElec/config_templates/bad_weather_config.json

# 特点：低能见度、高大气衰减、性能下降
```

### 夜间场景
```bash
# 夜间低照度条件
python run_simulation.py PhoElec/config_templates/night_scenario_config.json

# 特点：红外优势、可见光劣势、热对比增强
```

### 多目标场景
```bash
# 复杂多目标环境
python run_simulation.py PhoElec/config_templates/multi_target_config.json

# 特点：多设备协同、复杂交互、高计算负载
```

## 📊 按输出类型使用

### 仅生成图像
```bash
# 专注于图像生成，不生成数据文件
python run_simulation.py PhoElec/config_templates/image_only_config.json

# 输出：大量高质量图像文件
# 优势：快速、直观、适合演示
```

### 仅生成视频
```bash
# 专注于动态视频生成
python run_simulation.py PhoElec/config_templates/video_only_config.json

# 输出：连续动态视频文件
# 优势：展示时间演化、动态效果
```

### 仅生成数据
```bash
# 专注于参数数据生成
python run_simulation.py PhoElec/config_templates/data_only_config.json

# 输出：大量CSV/JSON数据文件
# 优势：高精度、适合分析、计算快速
```

## ⚡ 性能测试使用

### 标准性能测试
```bash
# 测试系统标准性能
python run_simulation.py PhoElec/config_templates/performance_test_config.json

# 监控：CPU使用率、内存占用、处理速度
```

### 压力测试
```bash
# 测试系统极限性能（需要更多资源）
python run_simulation.py PhoElec/config_templates/complete_config.json --threads 8

# 注意：可能需要较长时间和更多内存
```

## 🔧 自定义配置示例

### 修改基础配置
```bash
# 1. 复制基础配置
cp PhoElec/config_templates/basic_config.json my_config.json

# 2. 编辑配置文件
# 修改 data_count、duration 等参数

# 3. 运行自定义配置
python run_simulation.py my_config.json
```

### 组合不同设备
```json
{
  "simulation": { ... },
  "system": { ... },
  "optical_targets": [
    // 从 infrared_target_config.json 复制
  ],
  "optical_jammers": [
    // 从 jammer_config.json 复制
  ],
  "optical_recons": [
    // 从 recon_config.json 复制
  ]
}
```

## ⚠️ 使用注意事项

### 资源需求
- **minimal_config.json**: 最低资源需求
- **basic_config.json**: 中等资源需求
- **complete_config.json**: 高资源需求
- **multi_target_config.json**: 最高资源需求

## 🔍 故障排除

### 配置验证
```bash
# 验证配置文件格式
python -c "from PhoElec.utils.config_validator import validate_config_file; print(validate_config_file('your_config.json'))"
```

### 性能监控
```bash
# 启用详细日志
python run_simulation.py PhoElec/config_templates/basic_config.json --log-level DEBUG
```

### 资源限制
```bash
# 限制线程数
python run_simulation.py PhoElec/config_templates/complete_config.json --threads 2
```

"""
图像生成工具模块
提供640×480分辨率的图像和视频生成功能，支持中文字符显示
"""

import numpy as np
import cv2
import logging
from typing import Dict, List, Tuple, Optional, Any, Union
from PIL import Image, ImageDraw, ImageFont
import os
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
from datetime import datetime

from .logger import LoggerMixin


logger = logging.getLogger(__name__)


class ChineseFontManager(LoggerMixin):
    """中文字体管理器"""
    
    def __init__(self):
        """初始化中文字体管理器"""
        self.available_fonts = self._find_chinese_fonts()
        self.default_font = self._get_default_font()
        self.logger.info(f"找到 {len(self.available_fonts)} 个中文字体")
    
    def _find_chinese_fonts(self) -> List[str]:
        """查找系统中的中文字体"""
        chinese_fonts = []
        
        # 常见的中文字体名称
        common_chinese_fonts = [
            'SimHei',           # 黑体
            'SimSun',           # 宋体
            'Microsoft YaHei',  # 微软雅黑
            'NotoSansCJK',      # Noto Sans CJK
            'WenQuanYi',        # 文泉驿
            'DejaVu Sans',      # DejaVu Sans
            'Arial Unicode MS', # Arial Unicode MS
        ]
        
        # 在系统字体中查找
        system_fonts = fm.findSystemFonts()
        
        for font_path in system_fonts:
            try:
                font_name = fm.FontProperties(fname=font_path).get_name()
                for chinese_font in common_chinese_fonts:
                    if chinese_font.lower() in font_name.lower():
                        chinese_fonts.append(font_path)
                        break
            except:
                continue
        
        # 添加常见字体路径
        common_paths = [
            '/System/Library/Fonts/PingFang.ttc',  # macOS
            '/System/Library/Fonts/Helvetica.ttc',
            'C:/Windows/Fonts/simhei.ttf',         # Windows
            'C:/Windows/Fonts/simsun.ttc',
            'C:/Windows/Fonts/msyh.ttc',
            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',  # Linux
            '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                chinese_fonts.append(path)
        
        return list(set(chinese_fonts))  # 去重
    
    def _get_default_font(self) -> Optional[str]:
        """获取默认中文字体"""
        if self.available_fonts:
            return self.available_fonts[0]
        return None
    
    def get_font(self, size: int = 16, font_path: Optional[str] = None) -> ImageFont.ImageFont:
        """
        获取字体对象
        
        Args:
            size: 字体大小
            font_path: 字体路径，None表示使用默认字体
            
        Returns:
            字体对象
        """
        if font_path is None:
            font_path = self.default_font
        
        try:
            if font_path and os.path.exists(font_path):
                return ImageFont.truetype(font_path, size)
        except Exception as e:
            self.logger.warning(f"加载字体失败 {font_path}: {e}")
        
        # 回退到默认字体
        try:
            return ImageFont.load_default()
        except:
            # 最后的回退方案
            return ImageFont.load_default()
    
    def test_chinese_support(self, font_path: str) -> bool:
        """
        测试字体是否支持中文
        
        Args:
            font_path: 字体路径
            
        Returns:
            是否支持中文
        """
        try:
            font = ImageFont.truetype(font_path, 16)
            # 创建测试图像
            img = Image.new('RGB', (100, 50), 'white')
            draw = ImageDraw.Draw(img)
            
            # 尝试绘制中文字符
            test_text = "测试中文"
            draw.text((10, 10), test_text, fill='black', font=font)
            
            return True
        except Exception:
            return False


class ImageGenerator(LoggerMixin):
    """图像生成器"""
    
    def __init__(self, resolution: Tuple[int, int] = (640, 480)):
        """
        初始化图像生成器
        
        Args:
            resolution: 图像分辨率
        """
        self.resolution = resolution
        self.width, self.height = resolution
        self.font_manager = ChineseFontManager()
        
        # 设置matplotlib中文字体
        self._setup_matplotlib_chinese()
        
        self.logger.info(f"图像生成器初始化，分辨率: {self.width}x{self.height}")
    
    def _setup_matplotlib_chinese(self):
        """设置matplotlib中文字体"""
        try:
            if self.font_manager.default_font:
                plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
                plt.rcParams['axes.unicode_minus'] = False
        except Exception as e:
            self.logger.warning(f"设置matplotlib中文字体失败: {e}")
    
    def create_base_image(
        self,
        background_color: Tuple[int, int, int] = (50, 50, 50),
        noise_level: float = 0.1
    ) -> np.ndarray:
        """
        创建基础图像
        
        Args:
            background_color: 背景颜色 (R, G, B)
            noise_level: 噪声水平 (0-1)
            
        Returns:
            图像数组
        """
        # 创建基础图像
        image = np.full((self.height, self.width, 3), background_color, dtype=np.uint8)
        
        # 添加噪声
        if noise_level > 0:
            noise = np.random.normal(0, noise_level * 255, image.shape).astype(np.int16)
            image = np.clip(image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        return image
    
    def add_target(
        self,
        image: np.ndarray,
        center: Tuple[int, int],
        size: int,
        brightness: int = 200,
        target_type: str = 'circle'
    ) -> np.ndarray:
        """
        在图像中添加目标
        
        Args:
            image: 输入图像
            center: 目标中心位置
            size: 目标大小
            brightness: 目标亮度
            target_type: 目标类型 ('circle', 'rectangle', 'cross')
            
        Returns:
            添加目标后的图像
        """
        result_image = image.copy()
        x, y = center
        
        if target_type == 'circle':
            cv2.circle(result_image, (x, y), size, (brightness, brightness, brightness), -1)
        elif target_type == 'rectangle':
            cv2.rectangle(result_image, (x-size, y-size), (x+size, y+size), 
                         (brightness, brightness, brightness), -1)
        elif target_type == 'cross':
            cv2.line(result_image, (x-size, y), (x+size, y), 
                    (brightness, brightness, brightness), 3)
            cv2.line(result_image, (x, y-size), (x, y+size), 
                    (brightness, brightness, brightness), 3)
        
        return result_image
    
    def add_chinese_text(
        self,
        image: np.ndarray,
        text: str,
        position: Tuple[int, int],
        font_size: int = 16,
        color: Tuple[int, int, int] = (255, 255, 0),
        background_color: Optional[Tuple[int, int, int]] = None
    ) -> np.ndarray:
        """
        在图像中添加中文文本
        
        Args:
            image: 输入图像
            text: 文本内容
            position: 文本位置
            font_size: 字体大小
            color: 文本颜色
            background_color: 背景颜色，None表示透明
            
        Returns:
            添加文本后的图像
        """
        # 转换为PIL图像
        pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(pil_image)
        
        # 获取字体
        font = self.font_manager.get_font(font_size)
        
        # 添加背景
        if background_color is not None:
            # 计算文本边界框
            bbox = draw.textbbox(position, text, font=font)
            draw.rectangle(bbox, fill=background_color)
        
        # 绘制文本
        draw.text(position, text, fill=color, font=font)
        
        # 转换回OpenCV格式
        result_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        
        return result_image
    
    def add_info_panel(
        self,
        image: np.ndarray,
        info_dict: Dict[str, Any],
        panel_position: str = 'top_left',
        panel_size: Tuple[int, int] = (200, 150),
        background_alpha: float = 0.7
    ) -> np.ndarray:
        """
        添加信息面板
        
        Args:
            image: 输入图像
            info_dict: 信息字典
            panel_position: 面板位置 ('top_left', 'top_right', 'bottom_left', 'bottom_right')
            panel_size: 面板大小
            background_alpha: 背景透明度
            
        Returns:
            添加信息面板后的图像
        """
        result_image = image.copy()
        panel_width, panel_height = panel_size
        
        # 确定面板位置
        if panel_position == 'top_left':
            x, y = 10, 10
        elif panel_position == 'top_right':
            x, y = self.width - panel_width - 10, 10
        elif panel_position == 'bottom_left':
            x, y = 10, self.height - panel_height - 10
        else:  # bottom_right
            x, y = self.width - panel_width - 10, self.height - panel_height - 10
        
        # 创建半透明背景
        overlay = result_image.copy()
        cv2.rectangle(overlay, (x, y), (x + panel_width, y + panel_height), 
                     (0, 0, 0), -1)
        cv2.addWeighted(overlay, background_alpha, result_image, 1 - background_alpha, 0, result_image)
        
        # 添加信息文本
        font_size = 14
        line_height = 20
        current_y = y + 15
        
        for key, value in info_dict.items():
            if current_y + line_height > y + panel_height:
                break
            
            text = f"{key}: {value}"
            result_image = self.add_chinese_text(
                result_image, text, (x + 10, current_y), 
                font_size=font_size, color=(255, 255, 255)
            )
            current_y += line_height
        
        return result_image
    
    def create_thermal_image(
        self,
        temperature_map: np.ndarray,
        colormap: str = 'hot'
    ) -> np.ndarray:
        """
        创建热成像图像
        
        Args:
            temperature_map: 温度分布图
            colormap: 颜色映射
            
        Returns:
            热成像图像
        """
        # 归一化温度图
        normalized_temp = cv2.normalize(temperature_map, None, 0, 255, cv2.NORM_MINMAX)
        
        # 应用颜色映射
        if colormap == 'hot':
            colormap_cv = cv2.COLORMAP_HOT
        elif colormap == 'jet':
            colormap_cv = cv2.COLORMAP_JET
        elif colormap == 'inferno':
            colormap_cv = cv2.COLORMAP_INFERNO
        else:
            colormap_cv = cv2.COLORMAP_HOT
        
        thermal_image = cv2.applyColorMap(normalized_temp.astype(np.uint8), colormap_cv)
        
        # 调整到目标分辨率
        thermal_image = cv2.resize(thermal_image, self.resolution)
        
        return thermal_image
    
    def generate_scene_image(
        self,
        scene_params: Dict[str, Any],
        target_params: Optional[Dict[str, Any]] = None
    ) -> np.ndarray:
        """
        生成场景图像
        
        Args:
            scene_params: 场景参数
            target_params: 目标参数
            
        Returns:
            场景图像
        """
        # 创建基础图像
        bg_intensity = scene_params.get('background_intensity', 50)
        noise_level = scene_params.get('noise_level', 0.1)
        
        image = self.create_base_image(
            background_color=(bg_intensity, bg_intensity, bg_intensity),
            noise_level=noise_level
        )
        
        # 添加目标
        if target_params:
            center = target_params.get('center', (self.width//2, self.height//2))
            size = target_params.get('size', 10)
            brightness = target_params.get('brightness', 200)
            target_type = target_params.get('type', 'circle')
            
            image = self.add_target(image, center, size, brightness, target_type)
        
        # 添加信息面板
        info_dict = {
            '时间': datetime.now().strftime('%H:%M:%S'),
            '场景': scene_params.get('scene_name', '未知'),
            '目标': target_params.get('target_name', '无') if target_params else '无'
        }
        
        image = self.add_info_panel(image, info_dict)
        
        return image

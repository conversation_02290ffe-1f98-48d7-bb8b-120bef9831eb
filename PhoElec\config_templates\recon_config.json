{"simulation": {"scenario_name": "光电侦察设备专用仿真", "duration": 240.0, "time_step": 0.1, "data_count": 1200, "output_types": ["static_images", "parameters"], "environment": {"weather_condition": "clear_weather", "temperature": 290.15, "humidity": 0.55, "pressure": 101325, "wind_speed": 3.5, "visibility": 25000}}, "system": {"max_threads": 8, "image_resolution": [640, 480], "video_fps": 30, "random_seed": 6789}, "optical_recons": [{"model": "红外预警系统_MWIR", "position": {"latitude": 39.92, "longitude": 116.42, "altitude": 2500.0}, "performance_params": {"detection_range": 40000, "resolution": 0.015, "spectral_coverage": [3e-06, 5e-06], "sensitivity": 0.98, "field_of_view": 30.0, "analysis_bandwidth": 5000000, "processing_delay": 0.05, "false_alarm_rate": 0.005, "detection_probability": 0.98, "noise_equivalent_temperature": 0.02}, "work_mode": "continuous_surveillance", "detection_mode": "infrared_warning", "detection_algorithms": [{"name": "thermal_signature_detection", "enabled": true, "threshold": 0.85, "confidence_weight": 0.5, "temperature_threshold": 50.0}, {"name": "motion_correlation", "enabled": true, "threshold": 0.75, "confidence_weight": 0.3, "velocity_threshold": 10.0}, {"name": "shape_recognition", "enabled": true, "threshold": 0.8, "confidence_weight": 0.2}], "tracking_params": {"max_targets": 20, "track_initiation_threshold": 2, "track_termination_threshold": 8, "position_accuracy": 0.5, "velocity_accuracy": 0.2, "prediction_horizon": 10.0}, "sensor_specs": {"detector_type": "HgCdTe", "pixel_count": [640, 480], "pixel_pitch": 2e-05, "integration_time": 0.01, "cooling_temperature": 77}}, {"model": "激光告警器_全向", "position": {"latitude": 39.925, "longitude": 116.425, "altitude": 1800.0}, "performance_params": {"detection_range": 25000, "resolution": 0.1, "spectral_coverage": [4e-07, 1.7e-06], "sensitivity": 0.95, "field_of_view": 360.0, "response_time": 0.001, "angular_resolution": 1.0, "power_threshold": 1e-09}, "work_mode": "omnidirectional_monitoring", "detection_mode": "laser_warning", "detection_algorithms": [{"name": "wavelength_identification", "enabled": true, "threshold": 0.9, "confidence_weight": 0.4, "spectral_resolution": 1e-09}, {"name": "pulse_pattern_analysis", "enabled": true, "threshold": 0.8, "confidence_weight": 0.3, "temporal_resolution": 1e-09}, {"name": "direction_finding", "enabled": true, "threshold": 0.85, "confidence_weight": 0.3, "angular_accuracy": 0.5}], "sensor_array": [{"position": "front", "coverage_angle": 90.0, "sensitivity": 0.95}, {"position": "right", "coverage_angle": 90.0, "sensitivity": 0.95}, {"position": "back", "coverage_angle": 90.0, "sensitivity": 0.95}, {"position": "left", "coverage_angle": 90.0, "sensitivity": 0.95}]}, {"model": "光谱分析仪_高分辨率", "position": {"latitude": 39.915, "longitude": 116.415, "altitude": 2200.0}, "performance_params": {"detection_range": 35000, "resolution": 0.01, "spectral_coverage": [2e-07, 2e-05], "sensitivity": 0.99, "field_of_view": 15.0, "spectral_resolution": 1e-10, "temporal_resolution": 0.1, "dynamic_range": 80}, "work_mode": "spectral_intelligence", "detection_mode": "spectral_analysis", "analysis_capabilities": [{"type": "material_identification", "spectral_library_size": 10000, "matching_accuracy": 0.95}, {"type": "gas_detection", "detectable_gases": ["CO2", "H2O", "CH4", "NO2"], "concentration_accuracy": 0.1}, {"type": "temperature_measurement", "temperature_range": [200, 2000], "temperature_accuracy": 1.0}], "spectrometer_specs": {"grating_lines": 1200, "focal_length": 0.5, "entrance_slit_width": 1e-05, "detector_array_size": 2048}}]}
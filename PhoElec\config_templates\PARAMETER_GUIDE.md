# 光电对抗仿真系统配置参数详细指南

## 📋 配置文件结构

```json
{
  "simulation": { ... },    // 仿真配置
  "system": { ... },        // 系统配置
  "optical_targets": [...], // 光电目标设备
  "optical_jammers": [...], // 光电干扰设备
  "optical_recons": [...]   // 光电侦察设备
}
```

## 🔧 仿真配置 (simulation)

### 基础参数
- **scenario_name** (string): 仿真场景名称
- **duration** (float): 仿真时长，单位：秒 (1.0-3600.0)
- **time_step** (float): 时间步长，单位：秒 (0.01-1.0)
- **data_count** (int): 数据生成数量 (1-5000)
- **output_types** (array): 输出类型
  - `"static_images"`: 静态图像
  - `"dynamic_images"`: 动态视频
  - `"parameters"`: 参数数据

### 环境参数 (environment)
- **weather_condition** (string): 天气条件
  - `"clear_weather"`: 晴朗天气
  - `"haze"`: 霾
  - `"fog"`: 雾
  - `"rain"`: 雨
  - `"snow"`: 雪
- **temperature** (float): 温度，单位：K (200-350)
- **humidity** (float): 湿度 (0.0-1.0)
- **pressure** (float): 气压，单位：Pa (80000-110000)
- **wind_speed** (float): 风速，单位：m/s (0-30)
- **visibility** (float): 能见度，单位：m (100-50000)

### 扩展环境参数
- **cloud_cover** (float): 云量 (0.0-1.0)
- **precipitation** (float): 降水量，单位：mm/h (0-50)
- **atmospheric_turbulence** (float): 大气湍流强度 (0.0-1.0)
- **solar_elevation** (float): 太阳高度角，单位：度 (-90-90)
- **illumination_level** (float): 照明水平，单位：lux (0-100000)

## ⚙️ 系统配置 (system)

### 基础参数
- **max_threads** (int): 最大线程数 (1-32)
- **image_resolution** (array): 图像分辨率 [宽, 高]
  - 推荐: [640, 480]
- **video_fps** (int): 视频帧率 (1-60)
- **random_seed** (int): 随机种子 (1-999999)

### 性能参数
- **memory_limit_mb** (int): 内存限制，单位：MB (512-8192)
- **cpu_limit_percent** (int): CPU使用限制，单位：% (10-95)
- **log_level** (string): 日志级别
  - `"DEBUG"`, `"INFO"`, `"WARNING"`, `"ERROR"`
- **enable_performance_monitoring** (bool): 启用性能监控

## 🎯 光电目标设备 (optical_targets)

### 基础配置
- **model** (string): 设备型号名称
- **position** (object): 设备位置
  - **latitude** (float): 纬度 (-90-90)
  - **longitude** (float): 经度 (-180-180)
  - **altitude** (float): 海拔，单位：m (-1000-10000)
- **observation_direction** (object): 观察方向
  - **azimuth** (float): 方位角，单位：度 (0-360)
  - **elevation** (float): 俯仰角，单位：度 (-90-90)
- **work_mode** (string): 工作模式
  - `"passive_search"`: 被动搜索
  - `"active_illumination"`: 主动照明
  - `"target_designation"`: 目标指示

### 性能参数 (performance_params)
- **detection_range** (float): 探测距离，单位：m (100-100000)
- **resolution** (float): 分辨率，单位：mrad (0.001-1.0)
- **field_of_view** (float): 视场角，单位：度 (1-180)
- **spectral_range** (array): 光谱范围，单位：m [最小, 最大]
- **sensitivity** (float): 灵敏度 (0.1-1.0)

### 红外目标专用参数
- **temperature** (object): 温度分布
  - **engine** (float): 发动机温度，单位：K (300-2000)
  - **body** (float): 机体温度，单位：K (250-400)
  - **exhaust** (float): 排气温度，单位：K (400-1500)
  - **background** (float): 背景温度，单位：K (200-350)
- **component_areas** (object): 组件面积，单位：m²
- **emissivity** (object): 发射率 (0.1-1.0)

### 激光目标专用参数
- **wavelength** (float): 波长，单位：m (0.3e-6 - 15e-6)
- **power** (float): 功率，单位：W (1-10000)
- **beam_divergence** (float): 光束发散角，单位：rad (0.1e-3 - 10e-3)
- **pulse_duration** (float): 脉冲宽度，单位：s (1e-12 - 1e-3)
- **repetition_rate** (float): 重复频率，单位：Hz (1-10000)

## 🚫 光电干扰设备 (optical_jammers)

### 基础配置
- **model** (string): 设备型号名称
- **position** (object): 设备位置（同目标设备）
- **jamming_direction** (object): 干扰方向
  - **azimuth** (float): 方位角，单位：度 (0-360)
  - **elevation** (float): 俯仰角，单位：度 (-90-90)
- **work_mode** (string): 工作模式
  - `"continuous"`: 连续干扰
  - `"pulse"`: 脉冲干扰
  - `"burst"`: 突发干扰
- **jamming_strategy** (string): 干扰策略
  - `"area_denial"`: 区域拒止
  - `"sensor_overload"`: 传感器过载
  - `"decoy"`: 诱饵

### 性能参数 (performance_params)
- **jamming_power** (float): 干扰功率，单位：W (0-10000)
- **jamming_frequency** (float): 干扰频率，单位：Hz (0-10000)
- **coverage_range** (float): 覆盖距离，单位：m (100-50000)
- **duration** (float): 持续时间，单位：s (1-3600)

### 烟幕干扰专用参数
- **coverage_radius** (float): 覆盖半径，单位：m (10-1000)
- **extinction_coeff_visible** (float): 可见光消光系数 (1-50)
- **extinction_coeff_ir** (float): 红外消光系数 (1-30)
- **particle_density** (float): 粒子密度，单位：个/m³ (1e6-1e12)

### 激光干扰专用参数
- **wavelength** (float): 波长，单位：m (0.3e-6 - 15e-6)
- **beam_divergence** (float): 光束发散角，单位：rad (1e-3 - 50e-3)
- **pulse_duration** (float): 脉冲宽度，单位：s (1e-9 - 1e-3)
- **duty_cycle** (float): 占空比 (0.01-1.0)

## 🔍 光电侦察设备 (optical_recons)

### 基础配置
- **model** (string): 设备型号名称
- **position** (object): 设备位置（同目标设备）
- **work_mode** (string): 工作模式
  - `"passive_detection"`: 被动探测
  - `"active_scanning"`: 主动扫描
  - `"continuous_surveillance"`: 连续监视
- **detection_mode** (string): 探测模式
  - `"infrared_warning"`: 红外告警
  - `"laser_warning"`: 激光告警
  - `"spectral_analysis"`: 光谱分析

### 性能参数 (performance_params)
- **detection_range** (float): 探测距离，单位：m (100-100000)
- **resolution** (float): 分辨率，单位：mrad (0.001-1.0)
- **spectral_coverage** (array): 光谱覆盖，单位：m [最小, 最大]
- **sensitivity** (float): 灵敏度 (0.1-1.0)
- **field_of_view** (float): 视场角，单位：度 (1-360)
- **false_alarm_rate** (float): 虚警率 (0.001-0.1)
- **detection_probability** (float): 探测概率 (0.5-0.99)

### 高级参数
- **analysis_bandwidth** (float): 分析带宽，单位：Hz (1000-10000000)
- **processing_delay** (float): 处理延迟，单位：s (0.001-1.0)
- **multi_target_capacity** (int): 多目标处理能力 (1-50)

## 📊 参数范围和建议值

### 距离参数
- **近距离**: 100-1000m
- **中距离**: 1000-10000m
- **远距离**: 10000-50000m
- **超远距离**: >50000m

### 角度参数
- **方位角**: 0-360度（0度为正北）
- **俯仰角**: -90到90度（正值为向上）
- **视场角**: 1-180度（典型值5-30度）

### 功率参数
- **低功率**: 1-100W
- **中功率**: 100-1000W
- **高功率**: 1000-10000W

### 频率参数
- **低频**: 1-100Hz
- **中频**: 100-1000Hz
- **高频**: 1000-10000Hz

## ⚠️ 配置注意事项

1. **参数兼容性**: 确保设备参数之间的物理兼容性
2. **性能限制**: 注意系统资源限制，避免过高的参数设置
3. **物理合理性**: 参数值应符合实际物理规律
4. **环境一致性**: 环境参数应与设备性能参数匹配
5. **计算复杂度**: 高精度参数会增加计算时间

## 🔧 调试建议

1. **从简单开始**: 使用minimal_config.json开始测试
2. **逐步增加复杂度**: 逐步添加设备和参数
3. **性能监控**: 启用性能监控观察系统负载
4. **参数验证**: 使用配置验证工具检查参数合理性

# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['http_api.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('PhoElec', 'PhoElec'),
        ('PhoElec/config_templates', 'PhoElec/config_templates'),
    ],
    hiddenimports=[
        'uvicorn.lifespan.on',
        'uvicorn.lifespan.off',
        'uvicorn.protocols.websockets.auto',
        'uvicorn.protocols.websockets.websockets_impl',
        'uvicorn.protocols.http.auto',
        'uvicorn.protocols.http.h11_impl',
        'uvicorn.protocols.http.httptools_impl',
        'uvicorn.loops.auto',
        'uvicorn.loops.asyncio',
        'uvicorn.loops.uvloop',
        'fastapi',
        'fastapi.middleware.cors',
        'pydantic',
        'numpy',
        'cv2',
        'PIL',
        'matplotlib',
        'pandas',
        'h5py',
        'psutil',
        'json',
        'logging',
        'typing',
        'inspect',
        'asyncio',
        'asyncio.coroutines',
        'asyncio.base_events',
        'urllib.parse',
        'urllib.error',
        'api',
        'PhoElec',
        'PhoElec.core',
        'PhoElec.devices',
        'PhoElec.algorithms',
        'PhoElec.physics',
        'PhoElec.utils',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'test',
        'tests',
        'unittest',
        'doctest',
        'pdb',
        'pydoc',
        'sqlite3',
        'distutils',
        'setuptools',
        'pip',
        'wheel',
        'pytest',
        'IPython',
        'jupyter',
        'notebook',
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='http_api',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    cofile=None,
    icon=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='http_api'
)

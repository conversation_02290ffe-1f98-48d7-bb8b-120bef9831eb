# 光电对抗仿真系统

一个高度模块化的光电对抗仿真系统，支持红外、激光、电视等光电传感器的仿真，以及相应的干扰和侦察设备建模。

## 功能特性

### 核心功能
- **光电目标数据生成**: 支持静态/动态图像生成、参数数据计算（偏离范围、识别准确率、探测距离、探测概率）
- **光电干扰设备仿真**: 包括烟幕、红外诱饵弹、激光致盲等干扰设备的物理效应仿真
- **光电侦察设备建模**: 实现图像识别、特征提取、目标跟踪等功能
- **多线程并行处理**: 优化计算密集型任务的执行效率
- **640×480分辨率输出**: 支持中文字符显示的图像和视频生成

### 技术特点
- **科学准确的物理模型**: 基于斯蒂芬-玻尔兹曼定律、Beer-Lambert定律等物理原理
- **高度模块化设计**: 高内聚、低耦合的架构
- **统一的命令行接口**: JSON配置文件驱动
- **批次隔离的输出管理**: 每次调用产生独立的输出目录
- **性能监控和优化**: 内存管理、缓存机制、资源限制

## 系统架构

```
PhoElec/
├── main.py                 # 主入口文件
├── core/                   # 核心模块
│   ├── config_manager.py   # 配置管理
│   ├── output_manager.py   # 输出管理
│   └── simulation_engine.py # 仿真引擎
├── physics/                # 物理模型
│   ├── constants.py        # 物理常数
│   ├── radiation.py        # 辐射模型
│   ├── atmosphere.py       # 大气传输
│   └── detection.py        # 探测器模型
├── devices/                # 设备仿真
│   ├── optical_target.py   # 光电目标
│   ├── optical_jammer.py   # 光电干扰
│   └── optical_recon.py    # 光电侦察
├── utils/                  # 工具模块
│   ├── logger.py           # 日志系统
│   ├── parallel_processing.py # 并行处理
│   ├── performance.py      # 性能优化
│   ├── image_generator.py  # 图像生成
│   ├── video_generator.py  # 视频生成
│   ├── data_formatter.py   # 数据格式化
│   └── config_validator.py # 配置验证
└── config_templates/        # 配置示例
    └── ...
```

## 安装依赖

```bash
pip install numpy opencv-python pillow matplotlib pandas h5py psutil
```

## 使用方法

### 1. 基本使用

```bash
python run_simulation.py PhoElec/test_config.json
```

### 2. 指定输出目录

```bash
python run_simulation.py PhoElec/test_config.json --output-dir ./results
```

### 3. 设置日志级别

```bash
python run_simulation.py PhoElec/test_config.json --log-level DEBUG
```

### 4. 指定线程数

```bash
python run_simulation.py PhoElec/test_config.json --threads 8
```

### 5. 使用示例配置

```bash
python run_simulation.py PhoElec/config_templates/basic_config.json
```

## 配置文件格式

配置文件采用JSON格式，包含以下主要部分：

### 仿真配置
```json
{
  "simulation": {
    "scenario_name": "仿真场景名称",
    "duration": 120.0,
    "time_step": 0.1,
    "data_count": 1000,
    "output_types": ["static_images", "dynamic_images", "parameters"],
    "environment": {
      "weather_condition": "clear_weather",
      "temperature": 288.15,
      "humidity": 0.6
    }
  }
}
```

### 系统配置
```json
{
  "system": {
    "max_threads": 4,
    "image_resolution": [640, 480],
    "video_fps": 30,
    "random_seed": 12345
  }
}
```

### 设备配置
支持三类设备：
- `optical_targets`: 光电目标设备
- `optical_jammers`: 光电干扰设备  
- `optical_recons`: 光电侦察设备

详细配置示例请参考 `PhoElec/config_templates/basic_config.json`

## 输出结果

系统会在指定目录下创建以时间戳命名的会话目录，包含：

```
session_YYYYMMDD_HHMMSS_mmm/
├── images/          # 生成的图像文件
├── videos/          # 生成的视频文件
├── data/            # 参数数据文件（CSV/JSON格式）
├── logs/            # 日志文件
└── configs/         # 配置文件备份
```

## 性能指标

- **数据生成量**: 支持超过5000条数据生成
- **图像分辨率**: 640×480像素
- **视频帧率**: 30fps
- **并行处理**: 支持多线程加速
- **内存优化**: 自动垃圾回收和内存管理

## 系统测试

运行系统集成测试：

```bash
python run_tests.py
```

测试包括：
- 配置验证测试
- 核心模块测试
- 物理模型测试
- 设备仿真测试
- 完整仿真流程测试
- 性能指标测试

## 配置验证

验证配置文件格式：

```bash
python -c "from PhoElec.utils.config_validator import validate_config_file; print(validate_config_file('PhoElec/test_config.json'))"
```

## 注意事项

1. **物理准确性**: 所有物理模型基于科学原理，确保仿真结果的准确性
2. **中文支持**: 图像和视频输出支持中文字符显示
3. **资源管理**: 系统会自动监控CPU和内存使用情况
4. **批次隔离**: 每次运行产生独立的输出目录，避免文件冲突
5. **错误处理**: 完善的异常处理和日志记录机制

## 开发说明

### 添加新设备类型
1. 在 `devices/` 目录下创建新的设备仿真器
2. 继承相应的基类并实现必要的方法
3. 在 `simulation_engine.py` 中注册新设备类型

### 扩展物理模型
1. 在 `physics/` 目录下添加新的物理模型
2. 确保模型基于科学原理
3. 添加相应的单元测试

### 性能优化
1. 使用 `utils/parallel_processing.py` 进行并行化
2. 利用 `utils/performance.py` 进行性能监控
3. 合理使用缓存机制

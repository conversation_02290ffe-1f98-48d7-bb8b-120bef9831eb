#!/usr/bin/env python3
"""
光电对抗仿真系统HTTP API接口
基于FastAPI框架，封装run_simulation_api函数为HTTP接口
"""

import json
import logging
import os
import socket
from pathlib import Path
from typing import Dict, Any, Optional
from urllib.parse import quote, unquote
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from pydantic import BaseModel
import uvicorn

# 导入原始API函数
from api import run_simulation_api

# 服务配置
SERVER_PORT = 8587

def get_local_ipv4_address():
    """
    通过DNS获取本机IPv4局域网地址

    Returns:
        str: 本机IPv4地址，如果获取失败返回'127.0.0.1'
    """
    try:
        # 方法1: 通过连接外部地址获取本机IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            # 连接到一个外部地址（不会实际发送数据）
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            return local_ip
    except Exception:
        try:
            # 方法2: 通过主机名获取IP
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            # 避免返回127.0.0.1
            if local_ip != "127.0.0.1":
                return local_ip
        except Exception:
            pass

        try:
            # 方法3: 获取所有网络接口，选择非回环地址
            hostname = socket.gethostname()
            ip_list = socket.gethostbyname_ex(hostname)[2]
            for ip in ip_list:
                if not ip.startswith("127."):
                    return ip
        except Exception:
            pass

    # 如果所有方法都失败，返回回环地址
    return "127.0.0.1"


def url_encode_path(path_str: str) -> str:
    """
    对路径字符串进行URL编码

    Args:
        path_str: 原始路径字符串

    Returns:
        URL编码后的路径字符串
    """
    if not path_str:
        return path_str

    # 将路径分割为各个部分，分别编码，然后重新组合
    # 这样可以保持路径分隔符不被编码
    if isinstance(path_str, Path):
        path_str = str(path_str)

    # 处理Windows和Unix路径分隔符
    if '\\' in path_str:
        # Windows路径
        parts = path_str.split('\\')
        encoded_parts = [quote(part, safe='') for part in parts]
        return '/'.join(encoded_parts)  # 统一使用/作为URL路径分隔符
    else:
        # Unix路径或已经是URL格式
        parts = path_str.split('/')
        encoded_parts = [quote(part, safe='') for part in parts]
        return '/'.join(encoded_parts)


def url_encode_file_paths(data: Any) -> Any:
    """
    递归地对数据结构中的文件路径进行URL编码

    Args:
        data: 要处理的数据，可以是字典、列表或其他类型

    Returns:
        处理后的数据，其中的路径已被URL编码
    """
    if isinstance(data, dict):
        result = {}
        for key, value in data.items():
            # 检查是否是路径相关的字段
            path_related_keys = [
                'path', 'file_path', 'output_directory', 'directory', 'files',
                'images', 'videos', 'data', 'summary',  # 仿真结果类别
                'name'  # 文件名也需要编码
            ]
            is_path_field = (
                key in path_related_keys or
                key.endswith('_path') or
                key.endswith('_paths') or
                key.endswith('_directory') or
                key.endswith('_dir')
            )

            if is_path_field:
                if isinstance(value, str):
                    # 单个路径字符串
                    result[key] = url_encode_path(value)
                elif isinstance(value, list):
                    # 路径字符串列表
                    result[key] = [url_encode_path(item) if isinstance(item, str) else url_encode_file_paths(item) for item in value]
                else:
                    result[key] = url_encode_file_paths(value)
            else:
                result[key] = url_encode_file_paths(value)
        return result
    elif isinstance(data, list):
        return [url_encode_file_paths(item) for item in data]
    else:
        return data

# 创建FastAPI应用
app = FastAPI(
    title="光电对抗仿真系统API",
    description="光电对抗仿真系统HTTP接口",
    version="beta508152104"
)

# 添加CORS中间件，允许跨域
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 允许所有来源
    allow_credentials=True,
    allow_methods=["*"],  # 允许所有HTTP方法
    allow_headers=["*"],  # 允许所有请求头
)

# 设置simulation_results目录路径
SIMULATION_RESULTS_DIR = Path("./simulation_results")

# 确保simulation_results目录存在
SIMULATION_RESULTS_DIR.mkdir(parents=True, exist_ok=True)

# 挂载静态文件服务，开放simulation_results文件夹
app.mount("/simulation_results", StaticFiles(directory=str(SIMULATION_RESULTS_DIR)), name="simulation_results")

# 定义请求模型
class SimulationRequest(BaseModel):
    """仿真请求模型"""
    simulation: Dict[str, Any]
    system: Dict[str, Any]
    optical_targets: list
    optical_jammers: Optional[list] = None
    optical_recons: Optional[list] = None
    output_base_dir: Optional[str] = None
    log_level: Optional[str] = "INFO"
    num_threads: Optional[int] = None

# 定义响应模型
class SimulationResponse(BaseModel):
    """仿真响应模型"""
    success: bool
    session_info: Dict[str, Any]
    simulation_config: Dict[str, Any]
    simulation_results: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    server_info: Dict[str, Any]
    error_info: Optional[str] = None

@app.post("/run_simulation", response_model=SimulationResponse)
async def run_simulation(request: SimulationRequest):
    """
    运行光电对抗仿真
    
    Args:
        request: 仿真请求参数
        
    Returns:
        仿真结果
    """
    try:
        # 构建配置字典
        config_dict = {
            "simulation": request.simulation,
            "system": request.system,
            "optical_targets": request.optical_targets
        }
        
        # 添加可选字段
        if request.optical_jammers is not None:
            config_dict["optical_jammers"] = request.optical_jammers
        if request.optical_recons is not None:
            config_dict["optical_recons"] = request.optical_recons
        
        # 调用原始API函数
        result_json = run_simulation_api(
            config_input=config_dict,
            output_base_dir=request.output_base_dir,
            log_level=request.log_level,
            num_threads=request.num_threads
        )
        
        # 解析JSON结果
        result_dict = json.loads(result_json)

        # 对所有路径进行URL编码
        result_dict = url_encode_file_paths(result_dict)

        # 添加服务器信息
        ipv4_address = get_local_ipv4_address()
        server_info = {
            "ipv4_address": ipv4_address,
            "port": SERVER_PORT,
            "base_url": f"http://{ipv4_address}:{SERVER_PORT}",
            "static_files_url": f"http://{ipv4_address}:{SERVER_PORT}/simulation_results",
            "session_list_url": f"http://{ipv4_address}:{SERVER_PORT}/api/simulation_results"
        }
        result_dict["server_info"] = server_info

        return SimulationResponse(**result_dict)
        
    except Exception as e:
        # 记录错误
        logging.error(f"仿真执行失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"仿真执行失败: {str(e)}")

@app.get("/")
async def root():
    """根路径，返回API信息"""
    return {
        "message": "光电对抗仿真系统HTTP API",
        "version": "1.0.0",
        "endpoints": {
            "POST /run_simulation": "运行光电对抗仿真",
            "GET /": "API信息",
            "GET /health": "健康检查",
            "GET /api/simulation_results": "列出所有仿真会话",
            "GET /simulation_results/<path>": "下载仿真结果文件"
        },
        "static_files": {
            "/simulation_results": "仿真结果文件下载服务"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "message": "API服务正常运行"}


@app.get("/api/simulation_results")
async def list_simulation_sessions():
    """
    列出所有可用的仿真会话

    Returns:
        包含所有会话信息的字典
    """
    try:
        sessions = []

        if SIMULATION_RESULTS_DIR.exists():
            for session_dir in SIMULATION_RESULTS_DIR.iterdir():
                if session_dir.is_dir() and session_dir.name.startswith("session_"):
                    stat_info = session_dir.stat()
                    session_info = {
                        "session_id": url_encode_path(session_dir.name),
                        "created_time": getattr(stat_info, 'st_birthtime', stat_info.st_mtime),
                        "modified_time": stat_info.st_mtime,
                        "size_bytes": sum(f.stat().st_size for f in session_dir.rglob('*') if f.is_file()),
                        "subdirectories": []
                    }

                    # 获取子目录信息
                    for subdir in session_dir.iterdir():
                        if subdir.is_dir():
                            files = list(subdir.glob('*'))
                            file_count = len([f for f in files if f.is_file()])
                            # 对文件名进行URL编码
                            file_names = [f.name for f in files if f.is_file()][:10]
                            encoded_file_names = [url_encode_path(name) for name in file_names]

                            subdir_info = {
                                "name": url_encode_path(subdir.name),
                                "file_count": file_count,
                                "files": encoded_file_names  # URL编码后的文件名
                            }
                            session_info["subdirectories"].append(subdir_info)

                    sessions.append(session_info)

        # 按创建时间排序，最新的在前
        sessions.sort(key=lambda x: x["created_time"], reverse=True)

        return {
            "total_sessions": len(sessions),
            "sessions": sessions,
            "base_url": url_encode_path("/simulation_results")
        }

    except Exception as e:
        logging.error(f"列出仿真会话失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"列出仿真会话失败: {str(e)}")

if __name__ == "__main__":
    # 启动服务器
    print(f"启动光电对抗仿真系统HTTP API服务...")
    print(f"本机IPv4地址: {get_local_ipv4_address()}")
    print(f"服务端口: {SERVER_PORT}")
    print(f"服务地址: http://{get_local_ipv4_address()}:{SERVER_PORT}")
    print(f"API文档: http://{get_local_ipv4_address()}:{SERVER_PORT}/docs")
    print(f"静态文件: http://{get_local_ipv4_address()}:{SERVER_PORT}/simulation_results/")

    uvicorn.run(
        app,
        host="0.0.0.0",  # 监听所有地址
        port=SERVER_PORT,  # 使用定义的端口常量
        log_level="info"
    )

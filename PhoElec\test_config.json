{"simulation": {"scenario_name": "系统测试场景", "duration": 30.0, "time_step": 0.1, "data_count": 50, "output_types": ["static_images", "parameters"], "environment": {"weather_condition": "clear_weather", "temperature": 288.15, "humidity": 0.6, "pressure": 101325, "wind_speed": 5.0, "visibility": 23000}}, "system": {"max_threads": 2, "image_resolution": [640, 480], "video_fps": 30, "random_seed": 12345}, "optical_targets": [{"model": "测试红外目标", "position": {"latitude": 39.9042, "longitude": 116.4074, "altitude": 1000.0}, "observation_direction": {"azimuth": 45.0, "elevation": 10.0}, "performance_params": {"detection_range": 10000, "resolution": 0.1, "field_of_view": 10.0}, "work_mode": "passive_search"}], "optical_jammers": [{"model": "测试烟幕干扰", "position": {"latitude": 39.9, "longitude": 116.4, "altitude": 500.0}, "jamming_direction": {"azimuth": 90.0, "elevation": 0.0}, "performance_params": {"jamming_power": 500, "coverage_range": 2000}, "work_mode": "continuous", "jamming_strategy": "area_denial"}], "optical_recons": [{"model": "测试红外侦察", "position": {"latitude": 39.92, "longitude": 116.42, "altitude": 1500.0}, "performance_params": {"detection_range": 15000, "resolution": 0.05, "spectral_coverage": [3e-06, 1.2e-05]}, "work_mode": "passive_detection", "detection_mode": "infrared_warning"}]}
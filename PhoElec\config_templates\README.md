# 配置模板说明文档

本文件夹包含了光电对抗仿真系统的各种配置模板，展示了所有参数的配置作用和使用方法。

## 📁 配置模板列表

### 基础配置模板
- `basic_config.json` - 最简单的基础配置
- `minimal_config.json` - 最小化配置示例
- `complete_config.json` - 完整参数配置示例

### 设备专用配置
- `infrared_target_config.json` - 红外目标设备专用配置
- `laser_target_config.json` - 激光目标设备专用配置
- `tv_target_config.json` - 电视目标设备专用配置
- `jammer_config.json` - 干扰设备专用配置
- `recon_config.json` - 侦察设备专用配置

### 场景配置模板
- `clear_weather_config.json` - 晴朗天气场景
- `bad_weather_config.json` - 恶劣天气场景
- `night_scenario_config.json` - 夜间场景
- `multi_target_config.json` - 多目标场景
- `complex_scenario_config.json` - 复杂对抗场景

### 性能测试配置
- `performance_test_config.json` - 性能测试配置
- `stress_test_config.json` - 压力测试配置
- `quick_test_config.json` - 快速测试配置

### 输出专用配置
- `image_only_config.json` - 仅生成图像
- `video_only_config.json` - 仅生成视频
- `data_only_config.json` - 仅生成数据
- `high_resolution_config.json` - 高分辨率输出

## 🔧 参数说明

### 仿真配置 (simulation)
- `scenario_name`: 仿真场景名称
- `duration`: 仿真时长（秒）
- `time_step`: 时间步长（秒）
- `data_count`: 数据生成数量（1-5000）
- `output_types`: 输出类型列表
- `environment`: 环境参数配置

### 系统配置 (system)
- `max_threads`: 最大线程数（1-32）
- `image_resolution`: 图像分辨率 [宽, 高]
- `video_fps`: 视频帧率（1-60）
- `random_seed`: 随机种子

### 设备配置
- `model`: 设备型号名称
- `position`: 设备位置（经纬度、海拔）
- `performance_params`: 性能参数
- `work_mode`: 工作模式

## 🚀 使用方法

```bash
# 使用基础配置
python run_simulation.py PhoElec/config_templates/basic_config.json

# 使用复杂场景配置
python run_simulation.py PhoElec/config_templates/complex_scenario_config.json

# 使用性能测试配置
python run_simulation.py PhoElec/config_templates/performance_test_config.json
```

## ⚠️ 注意事项

1. 所有配置文件都经过验证，可以直接使用
2. 根据需要修改参数值，注意参数范围限制
3. 复杂配置可能需要更多计算时间和资源
4. 建议先使用基础配置熟悉系统功能

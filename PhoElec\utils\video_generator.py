"""
视频生成工具模块
提供视频生成和处理功能，支持中文字幕和标注
"""

import cv2
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any, Callable
from pathlib import Path
import time
from datetime import datetime, timedelta

from .image_generator import ImageGenerator, ChineseFontManager
from .logger import LoggerMixin


logger = logging.getLogger(__name__)


class VideoGenerator(LoggerMixin):
    """视频生成器"""
    
    def __init__(
        self,
        resolution: Tuple[int, int] = (640, 480),
        fps: int = 30,
        codec: str = 'mp4v'
    ):
        """
        初始化视频生成器
        
        Args:
            resolution: 视频分辨率
            fps: 帧率
            codec: 编码器
        """
        self.resolution = resolution
        self.fps = fps
        self.codec = codec
        self.width, self.height = resolution
        
        # 初始化图像生成器
        self.image_generator = ImageGenerator(resolution)
        
        self.logger.info(f"视频生成器初始化，分辨率: {self.width}x{self.height}, FPS: {fps}")
    
    def create_video_writer(self, output_path: str) -> cv2.VideoWriter:
        """
        创建视频写入器
        
        Args:
            output_path: 输出路径
            
        Returns:
            视频写入器
        """
        fourcc = cv2.VideoWriter_fourcc(*self.codec)
        writer = cv2.VideoWriter(output_path, fourcc, self.fps, self.resolution)
        
        if not writer.isOpened():
            raise RuntimeError(f"无法创建视频写入器: {output_path}")
        
        return writer
    
    def generate_dynamic_scene_video(
        self,
        output_path: str,
        duration: float,
        scene_generator: Callable[[float], Dict[str, Any]],
        progress_callback: Optional[Callable[[int, int], None]] = None
    ) -> str:
        """
        生成动态场景视频
        
        Args:
            output_path: 输出路径
            duration: 视频时长（秒）
            scene_generator: 场景生成函数，输入时间戳，返回场景参数
            progress_callback: 进度回调函数
            
        Returns:
            生成的视频文件路径
        """
        total_frames = int(duration * self.fps)
        writer = self.create_video_writer(output_path)
        
        try:
            for frame_idx in range(total_frames):
                timestamp = frame_idx / self.fps
                
                # 生成当前帧的场景参数
                scene_params = scene_generator(timestamp)
                
                # 生成图像
                frame = self._generate_frame(scene_params, timestamp)
                
                # 写入视频
                writer.write(frame)
                
                # 进度回调
                if progress_callback and (frame_idx + 1) % (self.fps * 5) == 0:
                    progress_callback(frame_idx + 1, total_frames)
            
            self.logger.info(f"视频生成完成: {output_path}")
            return output_path
            
        finally:
            writer.release()
    
    def _generate_frame(self, scene_params: Dict[str, Any], timestamp: float) -> np.ndarray:
        """
        生成单帧图像
        
        Args:
            scene_params: 场景参数
            timestamp: 时间戳
            
        Returns:
            帧图像
        """
        # 基础场景参数
        background_intensity = scene_params.get('background_intensity', 50)
        noise_level = scene_params.get('noise_level', 0.1)
        
        # 创建基础图像
        frame = self.image_generator.create_base_image(
            background_color=(background_intensity, background_intensity, background_intensity),
            noise_level=noise_level
        )
        
        # 添加目标
        targets = scene_params.get('targets', [])
        for target in targets:
            frame = self.image_generator.add_target(
                frame,
                center=target.get('center', (self.width//2, self.height//2)),
                size=target.get('size', 10),
                brightness=target.get('brightness', 200),
                target_type=target.get('type', 'circle')
            )
        
        # 添加时间戳
        time_str = f"时间: {timestamp:.2f}s"
        frame = self.image_generator.add_chinese_text(
            frame, time_str, (10, self.height - 30), 
            font_size=16, color=(255, 255, 0)
        )
        
        # 添加其他信息
        info_texts = scene_params.get('info_texts', [])
        for i, text in enumerate(info_texts):
            y_pos = 10 + i * 25
            frame = self.image_generator.add_chinese_text(
                frame, text, (10, y_pos), 
                font_size=14, color=(255, 255, 255)
            )
        
        return frame
    
    def create_target_tracking_video(
        self,
        output_path: str,
        duration: float,
        target_trajectory: List[Tuple[float, float]],
        target_info: Dict[str, Any]
    ) -> str:
        """
        创建目标跟踪视频
        
        Args:
            output_path: 输出路径
            duration: 视频时长
            target_trajectory: 目标轨迹 [(x, y), ...]
            target_info: 目标信息
            
        Returns:
            生成的视频文件路径
        """
        total_frames = int(duration * self.fps)
        writer = self.create_video_writer(output_path)
        
        try:
            for frame_idx in range(total_frames):
                timestamp = frame_idx / self.fps
                
                # 计算当前目标位置
                trajectory_progress = frame_idx / (total_frames - 1) if total_frames > 1 else 0
                trajectory_index = int(trajectory_progress * (len(target_trajectory) - 1))
                trajectory_index = min(trajectory_index, len(target_trajectory) - 1)
                
                current_position = target_trajectory[trajectory_index]
                
                # 生成帧
                frame = self._generate_tracking_frame(
                    current_position, target_info, timestamp, trajectory_progress
                )
                
                writer.write(frame)
            
            self.logger.info(f"目标跟踪视频生成完成: {output_path}")
            return output_path
            
        finally:
            writer.release()
    
    def _generate_tracking_frame(
        self,
        target_position: Tuple[float, float],
        target_info: Dict[str, Any],
        timestamp: float,
        progress: float
    ) -> np.ndarray:
        """生成跟踪帧"""
        # 创建基础图像
        frame = self.image_generator.create_base_image()
        
        # 添加目标
        x, y = int(target_position[0]), int(target_position[1])
        target_size = target_info.get('size', 15)
        target_brightness = target_info.get('brightness', 220)
        
        frame = self.image_generator.add_target(
            frame, (x, y), target_size, target_brightness, 'circle'
        )
        
        # 添加跟踪框
        box_size = target_size + 10
        cv2.rectangle(frame, (x-box_size, y-box_size), (x+box_size, y+box_size), 
                     (0, 255, 0), 2)
        
        # 添加十字线
        cv2.line(frame, (x-30, y), (x+30, y), (0, 255, 0), 1)
        cv2.line(frame, (x, y-30), (x, y+30), (0, 255, 0), 1)
        
        # 添加信息面板
        info_dict = {
            '目标类型': target_info.get('type', '未知'),
            '位置X': f"{x}",
            '位置Y': f"{y}",
            '跟踪状态': '正常',
            '置信度': f"{0.9 - progress * 0.2:.2f}"
        }
        
        frame = self.image_generator.add_info_panel(frame, info_dict)
        
        # 添加时间戳
        time_str = f"时间: {timestamp:.2f}s"
        frame = self.image_generator.add_chinese_text(
            frame, time_str, (10, self.height - 30), 
            font_size=16, color=(255, 255, 0)
        )
        
        return frame
    
    def create_interference_effect_video(
        self,
        output_path: str,
        duration: float,
        interference_type: str,
        interference_params: Dict[str, Any]
    ) -> str:
        """
        创建干扰效果视频
        
        Args:
            output_path: 输出路径
            duration: 视频时长
            interference_type: 干扰类型
            interference_params: 干扰参数
            
        Returns:
            生成的视频文件路径
        """
        total_frames = int(duration * self.fps)
        writer = self.create_video_writer(output_path)
        
        try:
            for frame_idx in range(total_frames):
                timestamp = frame_idx / self.fps
                
                # 生成干扰效果帧
                frame = self._generate_interference_frame(
                    interference_type, interference_params, timestamp
                )
                
                writer.write(frame)
            
            self.logger.info(f"干扰效果视频生成完成: {output_path}")
            return output_path
            
        finally:
            writer.release()
    
    def _generate_interference_frame(
        self,
        interference_type: str,
        params: Dict[str, Any],
        timestamp: float
    ) -> np.ndarray:
        """生成干扰效果帧"""
        # 创建基础图像
        frame = self.image_generator.create_base_image()
        
        if interference_type == 'smoke_screen':
            # 烟幕干扰效果
            frame = self._add_smoke_effect(frame, params, timestamp)
        elif interference_type == 'laser_dazzle':
            # 激光致盲效果
            frame = self._add_laser_dazzle_effect(frame, params, timestamp)
        elif interference_type == 'infrared_decoy':
            # 红外诱饵效果
            frame = self._add_infrared_decoy_effect(frame, params, timestamp)
        
        # 添加干扰信息
        info_dict = {
            '干扰类型': interference_type,
            '干扰强度': f"{params.get('intensity', 0.5):.2f}",
            '持续时间': f"{timestamp:.1f}s"
        }
        
        frame = self.image_generator.add_info_panel(frame, info_dict, 'top_right')
        
        return frame
    
    def _add_smoke_effect(self, frame: np.ndarray, params: Dict[str, Any], timestamp: float) -> np.ndarray:
        """添加烟幕效果"""
        intensity = params.get('intensity', 0.5)
        coverage = params.get('coverage', 0.3)
        
        # 创建烟幕遮罩
        mask = np.random.random((self.height, self.width)) < coverage
        
        # 应用烟幕效果
        smoke_color = int(100 * intensity)
        frame[mask] = [smoke_color, smoke_color, smoke_color]
        
        # 添加动态效果
        wave_effect = int(20 * np.sin(timestamp * 2))
        frame = np.roll(frame, wave_effect, axis=1)
        
        return frame
    
    def _add_laser_dazzle_effect(self, frame: np.ndarray, params: Dict[str, Any], timestamp: float) -> np.ndarray:
        """添加激光致盲效果"""
        intensity = params.get('intensity', 0.8)
        
        # 创建激光光斑
        center_x, center_y = self.width // 2, self.height // 2
        radius = int(50 * intensity)
        
        # 添加闪烁效果
        if int(timestamp * 10) % 2 == 0:
            cv2.circle(frame, (center_x, center_y), radius, (255, 255, 255), -1)
            
            # 添加光晕效果
            for i in range(3):
                cv2.circle(frame, (center_x, center_y), radius + i * 20, 
                          (200, 200, 255), 2)
        
        return frame
    
    def _add_infrared_decoy_effect(self, frame: np.ndarray, params: Dict[str, Any], timestamp: float) -> np.ndarray:
        """添加红外诱饵效果"""
        intensity = params.get('intensity', 0.7)
        
        # 添加多个热点
        num_decoys = params.get('num_decoys', 3)
        
        for i in range(num_decoys):
            x = int(self.width * (0.2 + 0.6 * i / max(1, num_decoys - 1)))
            y = int(self.height * (0.3 + 0.4 * np.sin(timestamp + i)))
            
            brightness = int(200 * intensity * (0.8 + 0.2 * np.sin(timestamp * 3 + i)))
            size = int(15 * (0.8 + 0.2 * np.cos(timestamp * 2 + i)))
            
            cv2.circle(frame, (x, y), size, (brightness, brightness, brightness), -1)
        
        return frame
    
    def add_chinese_subtitles(
        self,
        video_path: str,
        output_path: str,
        subtitles: List[Dict[str, Any]]
    ) -> str:
        """
        为视频添加中文字幕
        
        Args:
            video_path: 输入视频路径
            output_path: 输出视频路径
            subtitles: 字幕列表 [{'start': 0.0, 'end': 5.0, 'text': '字幕内容'}, ...]
            
        Returns:
            输出视频路径
        """
        cap = cv2.VideoCapture(video_path)
        writer = self.create_video_writer(output_path)
        
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = 0
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                timestamp = frame_count / fps
                
                # 查找当前时间的字幕
                current_subtitle = None
                for subtitle in subtitles:
                    if subtitle['start'] <= timestamp <= subtitle['end']:
                        current_subtitle = subtitle['text']
                        break
                
                # 添加字幕
                if current_subtitle:
                    frame = self.image_generator.add_chinese_text(
                        frame, current_subtitle, 
                        (50, self.height - 60), 
                        font_size=18, 
                        color=(255, 255, 255),
                        background_color=(0, 0, 0)
                    )
                
                writer.write(frame)
                frame_count += 1
            
            self.logger.info(f"字幕视频生成完成: {output_path}")
            return output_path
            
        finally:
            cap.release()
            writer.release()

#!/usr/bin/env python3
"""
API使用示例 - 展示新的功能特性
1. 支持字典和JSON字符串输入
2. 返回JSON字符串格式
3. 可选字段自动处理
"""

import json
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api import run_simulation_api, get_default_config


def example_1_dict_input():
    """示例1：使用字典输入"""
    print("="*60)
    print("示例1：使用字典输入")
    print("="*60)
    
    config = {
        "simulation": {
            "scenario_name": "字典输入示例",
            "duration": 10.0,
            "data_count": 5,
            "output_types": ["parameters"],
            "environment": {
                "weather_condition": "clear_weather",
                "temperature": 288.15,
                "humidity": 0.6
            }
        },
        "system": {
            "max_threads": 2,
            "image_resolution": [640, 480]
        },
        "optical_targets": [
            {
                "model": "示例目标",
                "position": {
                    "latitude": 39.9042,
                    "longitude": 116.4074,
                    "altitude": 1000.0
                },
                "observation_direction": {
                    "azimuth": 0.0,
                    "elevation": 0.0
                },
                "performance_params": {
                    "detection_range": 5000,
                    "resolution": 0.1
                },
                "work_mode": "passive_search"
            }
        ]
        # 注意：没有包含 optical_jammers 和 optical_recons
    }
    
    print("输入类型：字典")
    print("包含的顶级字段：", list(config.keys()))
    
    # 调用API
    result_json = run_simulation_api(config_input=config)
    result = json.loads(result_json)
    
    print(f"仿真结果：{'成功' if result['success'] else '失败'}")
    if result['success']:
        print(f"设备统计：{result['simulation_config']['device_count']}")
        print(f"输出目录：{result['session_info']['output_directory']}")


def example_2_json_string_input():
    """示例2：使用JSON字符串输入"""
    print("\n" + "="*60)
    print("示例2：使用JSON字符串输入")
    print("="*60)
    
    config_json = '''
    {
        "simulation": {
            "scenario_name": "JSON字符串输入示例",
            "duration": 10.0,
            "data_count": 5,
            "output_types": ["parameters"],
            "environment": {
                "weather_condition": "fog",
                "temperature": 278.15,
                "humidity": 0.9
            }
        },
        "system": {
            "max_threads": 2,
            "image_resolution": [640, 480]
        },
        "optical_targets": [
            {
                "model": "JSON示例目标",
                "position": {
                    "latitude": 40.0,
                    "longitude": 116.0,
                    "altitude": 1200.0
                },
                "observation_direction": {
                    "azimuth": 45.0,
                    "elevation": 10.0
                },
                "performance_params": {
                    "detection_range": 8000,
                    "resolution": 0.05
                },
                "work_mode": "active_tracking"
            }
        ],
        "optical_jammers": [
            {
                "model": "示例干扰器",
                "position": {
                    "latitude": 39.9,
                    "longitude": 116.1,
                    "altitude": 800.0
                },
                "jamming_direction": {
                    "azimuth": 90.0,
                    "elevation": 0.0
                },
                "performance_params": {
                    "jamming_power": 1000,
                    "coverage_range": 5000
                },
                "work_mode": "continuous",
                "jamming_strategy": "laser_blinding"
            }
        ]
    }
    '''
    
    print("输入类型：JSON字符串")
    print("字符串长度：", len(config_json))
    
    # 调用API
    result_json = run_simulation_api(config_input=config_json)
    result = json.loads(result_json)
    
    print(f"仿真结果：{'成功' if result['success'] else '失败'}")
    if result['success']:
        print(f"设备统计：{result['simulation_config']['device_count']}")
        print(f"输出目录：{result['session_info']['output_directory']}")


def example_3_json_output_processing():
    """示例3：JSON输出处理"""
    print("\n" + "="*60)
    print("示例3：JSON输出处理和分析")
    print("="*60)
    
    config = get_default_config()
    config["simulation"]["data_count"] = 3
    config["simulation"]["duration"] = 5.0
    
    # 调用API获取JSON字符串结果
    result_json = run_simulation_api(config_input=config)
    
    print("返回结果类型：", type(result_json))
    print("JSON字符串长度：", len(result_json))
    
    # 解析JSON结果
    result = json.loads(result_json)
    
    # 分析结果结构
    print("\n结果结构分析：")
    for key, value in result.items():
        if isinstance(value, dict):
            print(f"  {key}: 字典，包含 {len(value)} 个字段")
        elif isinstance(value, list):
            print(f"  {key}: 列表，包含 {len(value)} 个元素")
        else:
            print(f"  {key}: {type(value).__name__} = {value}")
    
    # 输出文件统计
    if result['success']:
        simulation_results = result.get('simulation_results', {})
        print(f"\n输出文件统计：")

        total_files = 0
        for category in ['images', 'videos', 'data', 'summary']:
            if category in simulation_results and isinstance(simulation_results[category], list):
                file_count = len(simulation_results[category])
                total_files += file_count
                if file_count > 0:
                    print(f"  {category}：{file_count} 个文件")

        print(f"  总文件数：{total_files}")


def example_4_error_handling():
    """示例4：错误处理"""
    print("\n" + "="*60)
    print("示例4：错误处理演示")
    print("="*60)
    
    # 测试无效JSON
    invalid_json = '{"simulation": {"invalid": json}}'
    print("测试无效JSON字符串...")
    
    try:
        result_json = run_simulation_api(config_input=invalid_json)
        result = json.loads(result_json)
        print(f"结果：{'成功' if result['success'] else '失败'}")
        if not result['success']:
            print(f"错误信息：{result['error_info']}")
    except Exception as e:
        print(f"异常：{e}")
    
    # 测试缺少必要字段
    incomplete_config = {
        "system": {
            "max_threads": 2
        }
        # 缺少 simulation 字段
    }
    
    print("\n测试缺少必要字段...")
    try:
        result_json = run_simulation_api(config_input=incomplete_config)
        result = json.loads(result_json)
        print(f"结果：{'成功' if result['success'] else '失败'}")
        if not result['success']:
            print(f"错误信息：{result['error_info']}")
    except Exception as e:
        print(f"异常：{e}")


def main():
    """主函数"""
    print("光电对抗仿真系统API使用示例")
    print("展示新功能：字典/JSON字符串输入，JSON字符串输出，可选字段处理")
    
    # 示例1：字典输入
    example_1_dict_input()
    
    # 示例2：JSON字符串输入
    example_2_json_string_input()
    
    # 示例3：JSON输出处理
    example_3_json_output_processing()
    
    # 示例4：错误处理
    example_4_error_handling()
    
    print("\n" + "="*60)
    print("所有示例完成！")
    print("="*60)


if __name__ == "__main__":
    main()

"""
大气传输模型
实现光电信号在大气中的传输特性计算
"""

import numpy as np
import logging
from typing import Dict, Tuple, Optional, Union

from .constants import (
    ATMOSPHERIC_PARAMS, ATMOSPHERIC_TRANSMISSION, SPEED_OF_LIGHT,
    WAVELENGTH_RANGES
)
from ..utils.logger import LoggerMixin


logger = logging.getLogger(__name__)


class AtmosphericTransmission(LoggerMixin):
    """大气传输模型"""
    
    def __init__(self, weather_condition: str = 'clear_weather'):
        """
        初始化大气传输模型
        
        Args:
            weather_condition: 天气条件 ('clear_weather', 'haze', 'fog', 'rain')
        """
        self.weather_condition = weather_condition
        self.transmission_params = ATMOSPHERIC_TRANSMISSION.get(
            weather_condition, ATMOSPHERIC_TRANSMISSION['clear_weather']
        )
        
        self.logger.debug(f"初始化大气传输模型，天气条件: {weather_condition}")
    
    def beer_lambert_transmission(
        self,
        distance: float,
        wavelength: float,
        extinction_coefficient: Optional[float] = None
    ) -> float:
        """
        Beer-Lambert定律计算大气透射率
        
        Args:
            distance: 传输距离 (m)
            wavelength: 波长 (m)
            extinction_coefficient: 消光系数 (m⁻¹)，None表示使用默认值
            
        Returns:
            透射率 (0-1)
        """
        if distance <= 0:
            return 1.0
        
        if extinction_coefficient is None:
            extinction_coefficient = self._get_extinction_coefficient(wavelength)
        
        # Beer-Lambert定律: T = exp(-β * d)
        optical_depth = extinction_coefficient * distance
        transmission = np.exp(-optical_depth)
        
        return max(0.0, min(1.0, transmission))
    
    def _get_extinction_coefficient(self, wavelength: float) -> float:
        """
        根据波长获取消光系数
        
        Args:
            wavelength: 波长 (m)
            
        Returns:
            消光系数 (m⁻¹)
        """
        base_coeff = self.transmission_params['extinction_coeff']
        
        # 波长相关的修正因子（简化模型）
        # 瑞利散射 ∝ λ⁻⁴，米散射 ∝ λ⁻¹
        reference_wavelength = 0.55e-6  # 参考波长 (m)
        
        if wavelength < 1e-6:  # 可见光和近红外
            # 瑞利散射占主导
            wavelength_factor = (reference_wavelength / wavelength)**4
        else:  # 中远红外
            # 米散射占主导
            wavelength_factor = (reference_wavelength / wavelength)**1
        
        return base_coeff * wavelength_factor
    
    def atmospheric_turbulence_effect(
        self,
        distance: float,
        beam_diameter: float,
        structure_constant: float = 1e-14
    ) -> Dict[str, float]:
        """
        计算大气湍流对光束的影响
        
        Args:
            distance: 传输距离 (m)
            beam_diameter: 光束直径 (m)
            structure_constant: 折射率结构常数 (m⁻²/³)
            
        Returns:
            湍流效应参数字典
        """
        # 弗里德参数（相干长度）
        wavelength = 1.064e-6  # 假设波长
        fried_parameter = (0.423 * (2 * np.pi / wavelength)**2 * 
                          structure_constant * distance)**(-3/5)
        
        # 光束扩展
        diffraction_limited_spread = wavelength * distance / beam_diameter
        turbulence_spread = np.sqrt((wavelength * distance / fried_parameter)**2)
        total_spread = np.sqrt(diffraction_limited_spread**2 + turbulence_spread**2)
        
        # 闪烁指数
        rytov_variance = 1.23 * structure_constant * (2 * np.pi / wavelength)**(7/6) * distance**(11/6)
        scintillation_index = np.exp(0.49 * rytov_variance / (1 + 1.11 * rytov_variance**(6/5))**(-5/6)) - 1
        
        return {
            'fried_parameter': fried_parameter,
            'beam_spread': total_spread,
            'scintillation_index': scintillation_index,
            'rytov_variance': rytov_variance
        }
    
    def molecular_absorption(
        self,
        wavelength: float,
        distance: float,
        humidity: float = 0.5,
        temperature: float = 288.15,
        pressure: float = 101325
    ) -> float:
        """
        计算分子吸收造成的衰减
        
        Args:
            wavelength: 波长 (m)
            distance: 距离 (m)
            humidity: 相对湿度 (0-1)
            temperature: 温度 (K)
            pressure: 气压 (Pa)
            
        Returns:
            透射率 (0-1)
        """
        # 简化的分子吸收模型
        # 主要考虑水蒸气和CO2的吸收
        
        # 水蒸气吸收系数（简化模型）
        water_vapor_density = self._calculate_water_vapor_density(humidity, temperature, pressure)
        water_absorption_coeff = self._water_vapor_absorption(wavelength, water_vapor_density)
        
        # CO2吸收系数
        co2_concentration = 400e-6  # 400 ppm
        co2_absorption_coeff = self._co2_absorption(wavelength, co2_concentration, pressure, temperature)
        
        # 总吸收系数
        total_absorption = water_absorption_coeff + co2_absorption_coeff
        
        # 透射率
        transmission = np.exp(-total_absorption * distance)
        
        return max(0.0, min(1.0, transmission))
    
    def _calculate_water_vapor_density(
        self,
        humidity: float,
        temperature: float,
        pressure: float
    ) -> float:
        """计算水蒸气密度"""
        # 饱和水蒸气压（Magnus公式）
        saturation_pressure = 611.2 * np.exp(17.67 * (temperature - 273.15) / (temperature - 29.65))
        
        # 实际水蒸气压
        vapor_pressure = humidity * saturation_pressure
        
        # 水蒸气密度
        water_vapor_density = vapor_pressure / (461.5 * temperature)  # kg/m³
        
        return water_vapor_density
    
    def _water_vapor_absorption(self, wavelength: float, density: float) -> float:
        """水蒸气吸收系数计算（简化模型）"""
        # 基于经验公式的简化模型
        wavelength_um = wavelength * 1e6
        
        if 1 <= wavelength_um <= 3:
            # 近红外波段
            absorption_coeff = density * 0.1 * np.exp(-(wavelength_um - 1.4)**2 / 0.5)
        elif 3 <= wavelength_um <= 15:
            # 中红外波段
            absorption_coeff = density * 0.5 * np.exp(-(wavelength_um - 6.3)**2 / 2.0)
        else:
            absorption_coeff = density * 0.01
        
        return absorption_coeff
    
    def _co2_absorption(
        self,
        wavelength: float,
        concentration: float,
        pressure: float,
        temperature: float
    ) -> float:
        """CO2吸收系数计算（简化模型）"""
        wavelength_um = wavelength * 1e6
        
        # CO2主要吸收带
        if 4.2 <= wavelength_um <= 4.4:
            # 4.3μm吸收带
            absorption_coeff = concentration * pressure / (ATMOSPHERIC_PARAMS['sea_level_pressure']) * 0.1
        elif 14 <= wavelength_um <= 16:
            # 15μm吸收带
            absorption_coeff = concentration * pressure / (ATMOSPHERIC_PARAMS['sea_level_pressure']) * 0.2
        else:
            absorption_coeff = 0.0
        
        return absorption_coeff
    
    def total_atmospheric_transmission(
        self,
        wavelength: float,
        distance: float,
        environmental_params: Optional[Dict] = None
    ) -> float:
        """
        计算总的大气透射率
        
        Args:
            wavelength: 波长 (m)
            distance: 距离 (m)
            environmental_params: 环境参数字典
            
        Returns:
            总透射率 (0-1)
        """
        if environmental_params is None:
            environmental_params = {}
        
        # 散射透射率
        scattering_transmission = self.beer_lambert_transmission(distance, wavelength)
        
        # 分子吸收透射率
        absorption_transmission = self.molecular_absorption(
            wavelength, distance,
            humidity=environmental_params.get('humidity', 0.5),
            temperature=environmental_params.get('temperature', 288.15),
            pressure=environmental_params.get('pressure', 101325)
        )
        
        # 总透射率
        total_transmission = scattering_transmission * absorption_transmission
        
        return max(0.0, min(1.0, total_transmission))


class AtmosphericNoise(LoggerMixin):
    """大气噪声模型"""
    
    @staticmethod
    def thermal_noise_power(
        temperature: float,
        bandwidth: float,
        wavelength: float
    ) -> float:
        """
        计算大气热噪声功率
        
        Args:
            temperature: 大气温度 (K)
            bandwidth: 带宽 (Hz)
            wavelength: 波长 (m)
            
        Returns:
            噪声功率 (W)
        """
        from .constants import BOLTZMANN_CONSTANT, PLANCK_CONSTANT
        
        frequency = SPEED_OF_LIGHT / wavelength
        
        # 普朗克噪声功率
        noise_power = PLANCK_CONSTANT * frequency * bandwidth / (
            np.exp(PLANCK_CONSTANT * frequency / (BOLTZMANN_CONSTANT * temperature)) - 1
        )
        
        return noise_power
    
    @staticmethod
    def background_radiance(
        temperature: float,
        wavelength_range: Tuple[float, float]
    ) -> float:
        """
        计算大气背景辐射亮度
        
        Args:
            temperature: 大气温度 (K)
            wavelength_range: 波长范围 (m)
            
        Returns:
            背景辐射亮度 (W⋅m⁻²⋅sr⁻¹)
        """
        from .radiation import BlackbodyRadiation
        
        return BlackbodyRadiation.spectral_radiance_band(
            temperature, wavelength_range
        )

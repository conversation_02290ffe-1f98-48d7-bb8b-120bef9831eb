"""
探测器物理模型
实现光电探测器的响应特性和信号处理
"""

import numpy as np
import logging
from typing import Dict, Tuple, Optional, List

from .constants import (
    DETECTOR_PARAMS, PLANCK_CONSTANT, SPEED_OF_LIGHT,
    BOLTZMANN_CONSTANT
)
from ..utils.logger import LoggerMixin


logger = logging.getLogger(__name__)


class PhotoDetector(LoggerMixin):
    """光电探测器模型"""
    
    def __init__(self, detector_type: str, detector_config: Dict):
        """
        初始化光电探测器
        
        Args:
            detector_type: 探测器类型
            detector_config: 探测器配置参数
        """
        self.detector_type = detector_type
        self.config = detector_config
        
        # 从预定义参数或配置中获取探测器参数
        default_params = DETECTOR_PARAMS.get(detector_type, {})
        
        self.spectral_range = detector_config.get(
            'spectral_range', default_params.get('spectral_range', (0.4e-6, 1.1e-6))
        )
        self.quantum_efficiency = detector_config.get(
            'quantum_efficiency', default_params.get('quantum_efficiency', 0.8)
        )
        self.dark_current = detector_config.get(
            'dark_current', default_params.get('dark_current', 1e-12)
        )
        self.responsivity = detector_config.get('responsivity', None)
        self.noise_equivalent_power = detector_config.get('nep', 1e-12)
        self.active_area = detector_config.get('active_area', 1e-6)  # m²
        
        self.logger.debug(f"初始化 {detector_type} 探测器")
    
    def calculate_responsivity(self, wavelength: float) -> float:
        """
        计算光谱响应度
        
        Args:
            wavelength: 波长 (m)
            
        Returns:
            响应度 (A/W)
        """
        if not (self.spectral_range[0] <= wavelength <= self.spectral_range[1]):
            return 0.0
        
        if self.responsivity is not None:
            return self.responsivity
        
        # 基于量子效率计算响应度
        # R(λ) = η * q * λ / (h * c)
        electron_charge = 1.602176634e-19  # C
        
        responsivity = (self.quantum_efficiency * electron_charge * wavelength / 
                       (PLANCK_CONSTANT * SPEED_OF_LIGHT))
        
        return responsivity
    
    def photocurrent(self, optical_power: float, wavelength: float) -> float:
        """
        计算光电流
        
        Args:
            optical_power: 入射光功率 (W)
            wavelength: 波长 (m)
            
        Returns:
            光电流 (A)
        """
        responsivity = self.calculate_responsivity(wavelength)
        return responsivity * optical_power
    
    def noise_current(self, bandwidth: float, temperature: float = 300) -> Dict[str, float]:
        """
        计算噪声电流
        
        Args:
            bandwidth: 带宽 (Hz)
            temperature: 温度 (K)
            
        Returns:
            各种噪声电流字典 (A)
        """
        # 暗电流噪声（散粒噪声）
        electron_charge = 1.602176634e-19
        dark_noise = np.sqrt(2 * electron_charge * self.dark_current * bandwidth)
        
        # 热噪声（约翰逊噪声）
        load_resistance = self.config.get('load_resistance', 1e6)  # Ω
        thermal_noise = np.sqrt(4 * BOLTZMANN_CONSTANT * temperature * bandwidth / load_resistance)
        
        # 总噪声电流
        total_noise = np.sqrt(dark_noise**2 + thermal_noise**2)
        
        return {
            'dark_noise': dark_noise,
            'thermal_noise': thermal_noise,
            'total_noise': total_noise
        }
    
    def signal_to_noise_ratio(
        self,
        optical_power: float,
        wavelength: float,
        bandwidth: float,
        temperature: float = 300
    ) -> float:
        """
        计算信噪比
        
        Args:
            optical_power: 入射光功率 (W)
            wavelength: 波长 (m)
            bandwidth: 带宽 (Hz)
            temperature: 温度 (K)
            
        Returns:
            信噪比 (dB)
        """
        signal_current = self.photocurrent(optical_power, wavelength)
        noise_currents = self.noise_current(bandwidth, temperature)
        noise_current = noise_currents['total_noise']
        
        if noise_current == 0:
            return float('inf')
        
        snr_linear = signal_current / noise_current
        snr_db = 20 * np.log10(snr_linear) if snr_linear > 0 else -float('inf')
        
        return snr_db
    
    def detectivity(self, wavelength: float, temperature: float = 300) -> float:
        """
        计算探测率 D*
        
        Args:
            wavelength: 波长 (m)
            temperature: 温度 (K)
            
        Returns:
            探测率 (cm⋅Hz^(1/2)/W)
        """
        responsivity = self.calculate_responsivity(wavelength)
        noise_currents = self.noise_current(1.0, temperature)  # 1 Hz带宽
        noise_current = noise_currents['total_noise']
        
        if noise_current == 0:
            return float('inf')
        
        # D* = R * sqrt(A) / i_n
        detectivity = responsivity * np.sqrt(self.active_area * 1e4) / noise_current  # cm⋅Hz^(1/2)/W
        
        return detectivity


class ImagingSensor(LoggerMixin):
    """成像传感器模型"""
    
    def __init__(self, sensor_config: Dict):
        """
        初始化成像传感器
        
        Args:
            sensor_config: 传感器配置参数
        """
        self.config = sensor_config
        
        # 传感器参数
        self.resolution = sensor_config.get('resolution', (640, 480))
        self.pixel_size = sensor_config.get('pixel_size', 5e-6)  # m
        self.fill_factor = sensor_config.get('fill_factor', 0.8)
        self.full_well_capacity = sensor_config.get('full_well_capacity', 100000)  # electrons
        self.read_noise = sensor_config.get('read_noise', 10)  # electrons RMS
        self.dark_current_density = sensor_config.get('dark_current_density', 1e-9)  # A/cm²
        
        # 光学参数
        self.focal_length = sensor_config.get('focal_length', 0.05)  # m
        self.f_number = sensor_config.get('f_number', 2.8)
        self.field_of_view = sensor_config.get('field_of_view', (10, 7.5))  # degrees
        
        # 初始化像素探测器
        pixel_area = (self.pixel_size**2) * self.fill_factor
        detector_config = {
            'active_area': pixel_area,
            'dark_current': self.dark_current_density * pixel_area * 1e4,  # A
            'quantum_efficiency': sensor_config.get('quantum_efficiency', 0.8)
        }
        
        self.pixel_detector = PhotoDetector('silicon', detector_config)
        
        self.logger.debug(f"初始化成像传感器，分辨率: {self.resolution}")
    
    def pixel_response(
        self,
        irradiance: float,
        wavelength: float,
        integration_time: float
    ) -> Dict[str, float]:
        """
        计算像素响应
        
        Args:
            irradiance: 像素面辐照度 (W/m²)
            wavelength: 波长 (m)
            integration_time: 积分时间 (s)
            
        Returns:
            像素响应参数字典
        """
        # 入射到像素的光功率
        pixel_area = (self.pixel_size**2) * self.fill_factor
        optical_power = irradiance * pixel_area
        
        # 光电流
        photocurrent = self.pixel_detector.photocurrent(optical_power, wavelength)
        
        # 积分电荷
        signal_charge = photocurrent * integration_time / (1.602176634e-19)  # electrons
        
        # 暗电流电荷
        dark_charge = (self.pixel_detector.dark_current * integration_time / 
                      (1.602176634e-19))  # electrons
        
        # 总电荷
        total_charge = signal_charge + dark_charge
        
        # 饱和检查
        is_saturated = total_charge > self.full_well_capacity
        if is_saturated:
            total_charge = self.full_well_capacity
        
        # 噪声
        shot_noise = np.sqrt(total_charge)  # 散粒噪声
        total_noise = np.sqrt(shot_noise**2 + self.read_noise**2)
        
        # 信噪比
        snr = signal_charge / total_noise if total_noise > 0 else 0
        
        return {
            'signal_charge': signal_charge,
            'dark_charge': dark_charge,
            'total_charge': total_charge,
            'noise': total_noise,
            'snr': snr,
            'is_saturated': is_saturated
        }
    
    def image_formation(
        self,
        scene_radiance: np.ndarray,
        wavelength: float,
        integration_time: float
    ) -> np.ndarray:
        """
        图像形成过程仿真
        
        Args:
            scene_radiance: 场景辐射亮度分布 (W⋅m⁻²⋅sr⁻¹)
            wavelength: 波长 (m)
            integration_time: 积分时间 (s)
            
        Returns:
            图像数组 (electrons)
        """
        # 光学系统收集立体角
        aperture_area = np.pi * (self.focal_length / (2 * self.f_number))**2
        solid_angle = aperture_area / (self.focal_length**2)
        
        # 像素面辐照度
        irradiance = scene_radiance * solid_angle
        
        # 初始化图像数组
        image = np.zeros(self.resolution)
        
        # 计算每个像素的响应
        for i in range(self.resolution[0]):
            for j in range(self.resolution[1]):
                pixel_irradiance = irradiance[i, j] if irradiance.shape == self.resolution else irradiance
                response = self.pixel_response(pixel_irradiance, wavelength, integration_time)
                image[i, j] = response['total_charge']
        
        return image
    
    def add_noise(self, image: np.ndarray) -> np.ndarray:
        """
        添加传感器噪声
        
        Args:
            image: 输入图像 (electrons)
            
        Returns:
            加噪声后的图像 (electrons)
        """
        # 散粒噪声
        shot_noise = np.random.poisson(np.maximum(image, 0)) - image
        
        # 读出噪声
        read_noise = np.random.normal(0, self.read_noise, image.shape)
        
        # 总噪声
        noisy_image = image + shot_noise + read_noise
        
        return np.maximum(noisy_image, 0)  # 确保非负
    
    def analog_to_digital_conversion(
        self,
        image: np.ndarray,
        bit_depth: int = 12,
        gain: float = 1.0
    ) -> np.ndarray:
        """
        模数转换
        
        Args:
            image: 输入图像 (electrons)
            bit_depth: 位深度
            gain: 增益 (DN/electron)
            
        Returns:
            数字图像 (DN)
        """
        # 转换为数字值
        digital_image = image * gain
        
        # 量化
        max_value = 2**bit_depth - 1
        digital_image = np.clip(digital_image, 0, max_value)
        digital_image = np.round(digital_image).astype(np.uint16)
        
        return digital_image

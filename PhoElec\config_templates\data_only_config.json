{"simulation": {"scenario_name": "仅数据输出场景", "duration": 300.0, "time_step": 0.1, "data_count": 3000, "output_types": ["parameters"], "environment": {"weather_condition": "clear_weather", "temperature": 288.15, "humidity": 0.5, "pressure": 101325, "wind_speed": 3.0, "visibility": 23000}}, "system": {"max_threads": 8, "image_resolution": [640, 480], "video_fps": 30, "random_seed": 33333}, "optical_targets": [{"model": "数据专用_精密红外目标", "position": {"latitude": 39.9042, "longitude": 116.4074, "altitude": 1000.0}, "observation_direction": {"azimuth": 0.0, "elevation": 0.0}, "performance_params": {"detection_range": 20000, "resolution": 0.05, "field_of_view": 10.0, "spectral_range": [3e-06, 5e-06], "sensitivity": 0.92, "noise_equivalent_temperature": 0.05, "measurement_accuracy": 0.01}, "work_mode": "precision_measurement", "temperature": {"engine": 550.0, "body": 310.0, "exhaust": 750.0, "background": 288.15}, "data_collection": {"sampling_rate": 1000, "measurement_precision": 0.001, "calibration_interval": 60, "statistical_analysis": true}}, {"model": "数据专用_高精度激光目标", "position": {"latitude": 39.91, "longitude": 116.41, "altitude": 1200.0}, "observation_direction": {"azimuth": 90.0, "elevation": 5.0}, "performance_params": {"detection_range": 25000, "resolution": 0.02, "field_of_view": 6.0, "wavelength": 1.064e-06, "power": 2500, "beam_divergence": 0.0001, "pulse_duration": 5e-09, "repetition_rate": 1000, "timing_accuracy": 1e-12}, "work_mode": "precision_ranging", "measurement_capabilities": {"range_accuracy": 0.01, "velocity_accuracy": 0.001, "angular_accuracy": 0.001, "power_stability": 0.1}}], "optical_jammers": [{"model": "数据专用_量化干扰器", "position": {"latitude": 39.9, "longitude": 116.4, "altitude": 500.0}, "jamming_direction": {"azimuth": 45.0, "elevation": 0.0}, "performance_params": {"jamming_power": 4000, "jamming_frequency": 2000, "coverage_range": 12000, "wavelength": 5.32e-07, "beam_divergence": 0.005, "power_stability": 0.05, "frequency_stability": 1e-06}, "work_mode": "quantified_jamming", "jamming_strategy": "parametric_interference", "measurement_parameters": {"power_monitoring": true, "frequency_analysis": true, "beam_quality_measurement": true, "effectiveness_quantification": true}}], "optical_recons": [{"model": "数据专用_分析型侦察", "position": {"latitude": 39.92, "longitude": 116.42, "altitude": 1500.0}, "performance_params": {"detection_range": 30000, "resolution": 0.015, "spectral_coverage": [3e-07, 1.5e-05], "sensitivity": 0.98, "field_of_view": 25.0, "analysis_bandwidth": 10000000, "processing_delay": 0.01, "false_alarm_rate": 0.001, "detection_probability": 0.99}, "work_mode": "analytical_surveillance", "detection_mode": "comprehensive_analysis", "data_analysis": {"spectral_analysis": true, "temporal_analysis": true, "spatial_analysis": true, "statistical_processing": true, "pattern_recognition": true, "machine_learning": true}, "measurement_outputs": {"target_classification": true, "threat_assessment": true, "behavior_analysis": true, "performance_metrics": true}}]}
"""
物理常数定义
包含光电对抗仿真中使用的各种物理常数
"""

import numpy as np

# 基础物理常数
STEFAN_BOLTZMANN_CONSTANT = 5.670374419e-8  # 斯蒂芬-玻尔兹曼常数 (W⋅m⁻²⋅K⁻⁴)
PLANCK_CONSTANT = 6.62607015e-34  # 普朗克常数 (J⋅s)
SPEED_OF_LIGHT = 299792458  # 光速 (m/s)
BOLTZMANN_CONSTANT = 1.380649e-23  # 玻尔兹曼常数 (J/K)

# 光学常数
WAVELENGTH_RANGES = {
    'visible': (0.38e-6, 0.78e-6),      # 可见光波长范围 (m)
    'near_infrared': (0.78e-6, 3e-6),   # 近红外波长范围 (m)
    'mid_infrared': (3e-6, 50e-6),      # 中红外波长范围 (m)
    'far_infrared': (50e-6, 1000e-6),   # 远红外波长范围 (m)
    'laser_1064nm': 1.064e-6,           # 常用激光波长 (m)
    'laser_532nm': 0.532e-6,            # 常用激光波长 (m)
}

# 大气参数
ATMOSPHERIC_PARAMS = {
    'sea_level_pressure': 101325,       # 海平面标准大气压 (Pa)
    'sea_level_temperature': 288.15,    # 海平面标准温度 (K)
    'temperature_lapse_rate': 0.0065,   # 温度递减率 (K/m)
    'gas_constant': 287.053,            # 干空气气体常数 (J/(kg⋅K))
    'gravity': 9.80665,                 # 重力加速度 (m/s²)
}

# 典型材料发射率
MATERIAL_EMISSIVITY = {
    'aluminum': 0.05,      # 铝
    'steel': 0.8,          # 钢
    'concrete': 0.9,       # 混凝土
    'paint_black': 0.95,   # 黑漆
    'paint_white': 0.9,    # 白漆
    'glass': 0.9,          # 玻璃
    'water': 0.96,         # 水
    'vegetation': 0.95,    # 植被
    'soil': 0.9,           # 土壤
}

# 典型目标温度 (K)
TYPICAL_TEMPERATURES = {
    'aircraft_engine': 800,     # 飞机发动机
    'aircraft_body': 250,       # 飞机机身
    'vehicle_engine': 400,      # 车辆发动机
    'vehicle_body': 300,        # 车辆车身
    'human_body': 310,          # 人体
    'background_day': 300,      # 白天背景
    'background_night': 280,    # 夜间背景
}

# 探测器参数
DETECTOR_PARAMS = {
    'silicon': {
        'spectral_range': (0.4e-6, 1.1e-6),    # 硅探测器光谱范围
        'quantum_efficiency': 0.8,              # 量子效率
        'dark_current': 1e-12,                  # 暗电流 (A)
    },
    'indium_gallium_arsenide': {
        'spectral_range': (0.9e-6, 1.7e-6),    # InGaAs探测器光谱范围
        'quantum_efficiency': 0.85,
        'dark_current': 1e-11,
    },
    'mercury_cadmium_telluride': {
        'spectral_range': (1e-6, 12e-6),       # HgCdTe探测器光谱范围
        'quantum_efficiency': 0.7,
        'dark_current': 1e-10,
    },
}

# 大气透射率参数（简化模型）
ATMOSPHERIC_TRANSMISSION = {
    'clear_weather': {
        'visibility': 23000,        # 能见度 (m)
        'extinction_coeff': 3.91 / 23000,  # 消光系数 (m⁻¹)
    },
    'haze': {
        'visibility': 5000,
        'extinction_coeff': 3.91 / 5000,
    },
    'fog': {
        'visibility': 1000,
        'extinction_coeff': 3.91 / 1000,
    },
    'rain': {
        'visibility': 2000,
        'extinction_coeff': 3.91 / 2000,
    },
}

# 激光参数
LASER_PARAMS = {
    'nd_yag_1064nm': {
        'wavelength': 1.064e-6,
        'beam_divergence': 1e-3,    # 光束发散角 (rad)
        'pulse_duration': 10e-9,    # 脉冲持续时间 (s)
        'repetition_rate': 10,      # 重复频率 (Hz)
    },
    'nd_yag_532nm': {
        'wavelength': 0.532e-6,
        'beam_divergence': 0.5e-3,
        'pulse_duration': 8e-9,
        'repetition_rate': 10,
    },
    'co2_laser': {
        'wavelength': 10.6e-6,
        'beam_divergence': 2e-3,
        'pulse_duration': 1e-6,
        'repetition_rate': 1000,
    },
}

# 干扰设备参数
JAMMING_PARAMS = {
    'smoke_screen': {
        'extinction_coeff_visible': 10,     # 可见光消光系数 (m⁻¹)
        'extinction_coeff_ir': 5,           # 红外消光系数 (m⁻¹)
        'duration': 300,                    # 持续时间 (s)
        'coverage_radius': 100,             # 覆盖半径 (m)
    },
    'infrared_decoy': {
        'temperature': 1000,                # 诱饵温度 (K)
        'emissivity': 0.9,                  # 发射率
        'burn_time': 60,                    # 燃烧时间 (s)
        'radiant_intensity': 1000,          # 辐射强度 (W/sr)
    },
    'laser_dazzler': {
        'power': 1000,                      # 激光功率 (W)
        'wavelength': 0.532e-6,             # 波长 (m)
        'beam_width': 10e-3,                # 光束宽度 (rad)
        'pulse_frequency': 1000,            # 脉冲频率 (Hz)
    },
}

# 数学常数
PI = np.pi
E = np.e

# 单位转换
DEGREES_TO_RADIANS = PI / 180
RADIANS_TO_DEGREES = 180 / PI
KELVIN_TO_CELSIUS = 273.15

{"simulation": {"scenario_name": "晴朗天气光电对抗场景", "duration": 180.0, "time_step": 0.1, "data_count": 800, "output_types": ["static_images", "dynamic_images", "parameters"], "environment": {"weather_condition": "clear_weather", "temperature": 298.15, "humidity": 0.45, "pressure": 101325, "wind_speed": 2.0, "visibility": 30000, "cloud_cover": 0.0, "precipitation": 0.0, "atmospheric_turbulence": 0.1, "solar_elevation": 45.0, "solar_azimuth": 180.0, "illumination_level": 100000}}, "system": {"max_threads": 6, "image_resolution": [640, 480], "video_fps": 30, "random_seed": 7890}, "optical_targets": [{"model": "晴天红外目标_高对比度", "position": {"latitude": 39.9042, "longitude": 116.4074, "altitude": 2000.0}, "observation_direction": {"azimuth": 45.0, "elevation": 20.0}, "performance_params": {"detection_range": 25000, "resolution": 0.05, "field_of_view": 15.0, "spectral_range": [3e-06, 5e-06], "sensitivity": 0.9}, "work_mode": "passive_search", "temperature": {"engine": 550.0, "body": 310.0, "exhaust": 750.0, "background": 298.15}}], "optical_jammers": [{"model": "晴天激光干扰器_高功率", "position": {"latitude": 39.9, "longitude": 116.4, "altitude": 1000.0}, "jamming_direction": {"azimuth": 90.0, "elevation": 10.0}, "performance_params": {"jamming_power": 8000, "jamming_frequency": 1500, "coverage_range": 15000, "wavelength": 5.32e-07, "beam_divergence": 0.003, "atmospheric_transmission": 0.95}, "work_mode": "continuous", "jamming_strategy": "sensor_overload"}], "optical_recons": [{"model": "晴天光电侦察_远程", "position": {"latitude": 39.92, "longitude": 116.42, "altitude": 3000.0}, "performance_params": {"detection_range": 50000, "resolution": 0.01, "spectral_coverage": [3e-07, 1.5e-05], "sensitivity": 0.98, "field_of_view": 20.0, "atmospheric_clarity_factor": 0.95}, "work_mode": "long_range_surveillance", "detection_mode": "multi_spectral_analysis"}]}
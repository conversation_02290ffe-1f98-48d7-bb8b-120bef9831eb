{"success": true, "session_info": {"start_time": "2025-08-15T22:51:40.844053", "end_time": "2025-08-15T22:52:02.333639", "duration": 21.489586, "session_id": "session_20250815_225140_844", "output_directory": "simulation_results/session_20250815_225140_844"}, "simulation_config": {"scenario_name": "多目标光电对抗场景", "duration": 10.0, "time_step": 0.1, "data_count": 3, "output_types": ["static_images", "dynamic_images", "parameters"], "device_count": {"optical_targets": 4, "optical_jammers": 2, "optical_recons": 2}, "environment": {"weather_condition": "clear_weather", "temperature": 291.15, "humidity": 0.6, "pressure": 101325, "wind_speed": 5.0, "visibility": 25000}, "system_config": {"max_threads": 8, "image_resolution": [640, 480], "video_fps": 30, "random_seed": 1234}}, "simulation_results": {"images": ["simulation_results/session_20250815_225140_844/images/target_0_static_0000.png", "simulation_results/session_20250815_225140_844/images/target_0_static_0001.png", "simulation_results/session_20250815_225140_844/images/target_0_static_0002.png", "simulation_results/session_20250815_225140_844/images/target_1_static_0000.png", "simulation_results/session_20250815_225140_844/images/target_1_static_0001.png", "simulation_results/session_20250815_225140_844/images/target_1_static_0002.png", "simulation_results/session_20250815_225140_844/images/target_2_static_0000.png", "simulation_results/session_20250815_225140_844/images/target_2_static_0001.png", "simulation_results/session_20250815_225140_844/images/target_2_static_0002.png", "simulation_results/session_20250815_225140_844/images/target_3_static_0000.png", "simulation_results/session_20250815_225140_844/images/target_3_static_0001.png", "simulation_results/session_20250815_225140_844/images/target_3_static_0002.png"], "videos": ["simulation_results/session_20250815_225140_844/videos/target_0_dynamic.mp4", "simulation_results/session_20250815_225140_844/videos/target_1_dynamic.mp4", "simulation_results/session_20250815_225140_844/videos/target_2_dynamic.mp4", "simulation_results/session_20250815_225140_844/videos/target_3_dynamic.mp4"], "data": ["simulation_results/session_20250815_225140_844/data/target_0_parameters.json", "simulation_results/session_20250815_225140_844/data/target_1_parameters.json", "simulation_results/session_20250815_225140_844/data/target_2_parameters.json", "simulation_results/session_20250815_225140_844/data/target_3_parameters.json", "simulation_results/session_20250815_225140_844/data/jammer_0_comprehensive.json", "simulation_results/session_20250815_225140_844/data/jammer_1_comprehensive.json", "simulation_results/session_20250815_225140_844/data/recon_0_comprehensive.json", "simulation_results/session_20250815_225140_844/data/recon_1_comprehensive.json", "simulation_results/session_20250815_225140_844/data/interference_analysis.json"], "summary": ["simulation_results/session_20250815_225140_844/data/simulation_summary.json"], "simulation_data": {"targets": [{"device_info": {"device_id": 0, "model": "多目标_红外目标A", "device_type": "optical_target", "generation_timestamp": "2025-08-15T22:52:02.277641"}, "parameter_data": {"deviation_range": [{"sample_id": 0, "azimuth_deviation_mrad": 47.066, "elevation_deviation_mrad": 77.284, "total_deviation_mrad": 90.488, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.277641"}, {"sample_id": 1, "azimuth_deviation_mrad": 59.21, "elevation_deviation_mrad": 32.91, "total_deviation_mrad": 67.742, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.277641"}, {"sample_id": 2, "azimuth_deviation_mrad": 37.387, "elevation_deviation_mrad": 74.705, "total_deviation_mrad": 83.538, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.277641"}], "recognition_accuracy": [{"sample_id": 0, "recognition_accuracy": 0.499, "distance_m": 15076.5, "weather_factor": 0.913, "distance_factor": 0.704, "timestamp": "2025-08-15T22:52:02.277641"}, {"sample_id": 1, "recognition_accuracy": 0.582, "distance_m": 7546.4, "weather_factor": 0.938, "distance_factor": 0.862, "timestamp": "2025-08-15T22:52:02.277641"}, {"sample_id": 2, "recognition_accuracy": 0.481, "distance_m": 14713.3, "weather_factor": 0.749, "distance_factor": 0.711, "timestamp": "2025-08-15T22:52:02.277641"}], "detection_range": [{"sample_id": 0, "detection_range_m": 20110.9, "base_range_m": 20000, "weather_factor": 0.9, "target_contrast": 1.117, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.277641"}, {"sample_id": 1, "detection_range_m": 14879.6, "base_range_m": 20000, "weather_factor": 0.952, "target_contrast": 0.781, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.277641"}, {"sample_id": 2, "detection_range_m": 23540.7, "base_range_m": 20000, "weather_factor": 0.986, "target_contrast": 1.194, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.277641"}], "detection_probability": [{"sample_id": 0, "detection_probability": 0.724, "distance_m": 5017.6, "base_probability": 0.95, "weather_factor": 0.928, "noise_factor": 0.821, "timestamp": "2025-08-15T22:52:02.277641"}, {"sample_id": 1, "detection_probability": 0.206, "distance_m": 23464.4, "base_probability": 0.281, "weather_factor": 0.865, "noise_factor": 0.848, "timestamp": "2025-08-15T22:52:02.277641"}, {"sample_id": 2, "detection_probability": 0.072, "distance_m": 29852.1, "base_probability": 0.097, "weather_factor": 0.821, "noise_factor": 0.911, "timestamp": "2025-08-15T22:52:02.277641"}]}, "statistics": {"total_samples": 3, "data_categories": 4, "sample_count_per_category": {"deviation_range": 3, "recognition_accuracy": 3, "detection_range": 3, "detection_probability": 3}}}, {"device_info": {"device_id": 1, "model": "多目标_红外目标B", "device_type": "optical_target", "generation_timestamp": "2025-08-15T22:52:02.328333"}, "parameter_data": {"deviation_range": [{"sample_id": 0, "azimuth_deviation_mrad": 121.994, "elevation_deviation_mrad": 97.307, "total_deviation_mrad": 156.048, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.328139"}, {"sample_id": 1, "azimuth_deviation_mrad": 80.476, "elevation_deviation_mrad": 99.83, "total_deviation_mrad": 128.228, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.328139"}, {"sample_id": 2, "azimuth_deviation_mrad": 94.556, "elevation_deviation_mrad": 66.267, "total_deviation_mrad": 115.465, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.328139"}], "recognition_accuracy": [{"sample_id": 0, "recognition_accuracy": 0.634, "distance_m": 13339.8, "weather_factor": 0.894, "distance_factor": 0.71, "timestamp": "2025-08-15T22:52:02.328139"}, {"sample_id": 1, "recognition_accuracy": 0.466, "distance_m": 13286.2, "weather_factor": 0.844, "distance_factor": 0.711, "timestamp": "2025-08-15T22:52:02.328139"}, {"sample_id": 2, "recognition_accuracy": 0.397, "distance_m": 15265.5, "weather_factor": 0.831, "distance_factor": 0.664, "timestamp": "2025-08-15T22:52:02.328139"}], "detection_range": [{"sample_id": 0, "detection_range_m": 21600.0, "base_range_m": 18000, "weather_factor": 0.977, "target_contrast": 1.296, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.328139"}, {"sample_id": 1, "detection_range_m": 21600.0, "base_range_m": 18000, "weather_factor": 0.952, "target_contrast": 1.48, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.328139"}, {"sample_id": 2, "detection_range_m": 10319.5, "base_range_m": 18000, "weather_factor": 0.955, "target_contrast": 0.601, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.328333"}], "detection_probability": [{"sample_id": 0, "detection_probability": 0.082, "distance_m": 25884.6, "base_probability": 0.116, "weather_factor": 0.847, "noise_factor": 0.837, "timestamp": "2025-08-15T22:52:02.328333"}, {"sample_id": 1, "detection_probability": 0.652, "distance_m": 10031.1, "base_probability": 0.766, "weather_factor": 0.884, "noise_factor": 0.963, "timestamp": "2025-08-15T22:52:02.328333"}, {"sample_id": 2, "detection_probability": 0.077, "distance_m": 25609.5, "base_probability": 0.122, "weather_factor": 0.781, "noise_factor": 0.811, "timestamp": "2025-08-15T22:52:02.328333"}]}, "statistics": {"total_samples": 3, "data_categories": 4, "sample_count_per_category": {"deviation_range": 3, "recognition_accuracy": 3, "detection_range": 3, "detection_probability": 3}}}, {"device_info": {"device_id": 2, "model": "多目标_激光目标C", "device_type": "optical_target", "generation_timestamp": "2025-08-15T22:52:02.123640"}, "parameter_data": {"deviation_range": [{"sample_id": 0, "azimuth_deviation_mrad": 15.646, "elevation_deviation_mrad": 40.669, "total_deviation_mrad": 43.575, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.123140"}, {"sample_id": 1, "azimuth_deviation_mrad": 25.386, "elevation_deviation_mrad": 35.802, "total_deviation_mrad": 43.888, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.123640"}, {"sample_id": 2, "azimuth_deviation_mrad": 25.962, "elevation_deviation_mrad": 19.418, "total_deviation_mrad": 32.42, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.123640"}], "recognition_accuracy": [{"sample_id": 0, "recognition_accuracy": 0.546, "distance_m": 4849.6, "weather_factor": 0.822, "distance_factor": 0.936, "timestamp": "2025-08-15T22:52:02.123640"}, {"sample_id": 1, "recognition_accuracy": 0.965, "distance_m": 1349.9, "weather_factor": 0.952, "distance_factor": 0.994, "timestamp": "2025-08-15T22:52:02.123640"}, {"sample_id": 2, "recognition_accuracy": 0.556, "distance_m": 11902.9, "weather_factor": 0.783, "distance_factor": 0.818, "timestamp": "2025-08-15T22:52:02.123640"}], "detection_range": [{"sample_id": 0, "detection_range_m": 24375.5, "base_range_m": 25000, "weather_factor": 0.958, "target_contrast": 1.018, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.123640"}, {"sample_id": 1, "detection_range_m": 15226.9, "base_range_m": 25000, "weather_factor": 0.973, "target_contrast": 0.626, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.123640"}, {"sample_id": 2, "detection_range_m": 21521.9, "base_range_m": 25000, "weather_factor": 0.982, "target_contrast": 0.877, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.123640"}], "detection_probability": [{"sample_id": 0, "detection_probability": 0.527, "distance_m": 21126.8, "base_probability": 0.593, "weather_factor": 0.984, "noise_factor": 0.903, "timestamp": "2025-08-15T22:52:02.123640"}, {"sample_id": 1, "detection_probability": 0.215, "distance_m": 30385.6, "base_probability": 0.244, "weather_factor": 0.925, "noise_factor": 0.953, "timestamp": "2025-08-15T22:52:02.123640"}, {"sample_id": 2, "detection_probability": 0.214, "distance_m": 29672.5, "base_probability": 0.268, "weather_factor": 0.89, "noise_factor": 0.896, "timestamp": "2025-08-15T22:52:02.123640"}]}, "statistics": {"total_samples": 3, "data_categories": 4, "sample_count_per_category": {"deviation_range": 3, "recognition_accuracy": 3, "detection_range": 3, "detection_probability": 3}}}, {"device_info": {"device_id": 3, "model": "多目标_电视目标D", "device_type": "optical_target", "generation_timestamp": "2025-08-15T22:52:02.018140"}, "parameter_data": {"deviation_range": [{"sample_id": 0, "azimuth_deviation_mrad": 72.142, "elevation_deviation_mrad": 111.253, "total_deviation_mrad": 132.596, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.018140"}, {"sample_id": 1, "azimuth_deviation_mrad": 171.886, "elevation_deviation_mrad": 57.984, "total_deviation_mrad": 181.403, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.018140"}, {"sample_id": 2, "azimuth_deviation_mrad": 119.854, "elevation_deviation_mrad": 69.746, "total_deviation_mrad": 138.67, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.018140"}], "recognition_accuracy": [{"sample_id": 0, "recognition_accuracy": 0.495, "distance_m": 11224.1, "weather_factor": 0.821, "distance_factor": 0.708, "timestamp": "2025-08-15T22:52:02.018140"}, {"sample_id": 1, "recognition_accuracy": 0.635, "distance_m": 3677.2, "weather_factor": 0.807, "distance_factor": 0.924, "timestamp": "2025-08-15T22:52:02.018140"}, {"sample_id": 2, "recognition_accuracy": 0.431, "distance_m": 12740.2, "weather_factor": 0.913, "distance_factor": 0.665, "timestamp": "2025-08-15T22:52:02.018140"}], "detection_range": [{"sample_id": 0, "detection_range_m": 18000.0, "base_range_m": 15000, "weather_factor": 0.994, "target_contrast": 1.331, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.018140"}, {"sample_id": 1, "detection_range_m": 18000.0, "base_range_m": 15000, "weather_factor": 0.914, "target_contrast": 1.452, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.018140"}, {"sample_id": 2, "detection_range_m": 17137.8, "base_range_m": 15000, "weather_factor": 0.958, "target_contrast": 1.192, "weather_condition": "clear_weather", "timestamp": "2025-08-15T22:52:02.018140"}], "detection_probability": [{"sample_id": 0, "detection_probability": 0.054, "distance_m": 22425.0, "base_probability": 0.096, "weather_factor": 0.705, "noise_factor": 0.802, "timestamp": "2025-08-15T22:52:02.018140"}, {"sample_id": 1, "detection_probability": 0.333, "distance_m": 15210.7, "base_probability": 0.477, "weather_factor": 0.787, "noise_factor": 0.887, "timestamp": "2025-08-15T22:52:02.018140"}, {"sample_id": 2, "detection_probability": 0.11, "distance_m": 20520.0, "base_probability": 0.147, "weather_factor": 0.845, "noise_factor": 0.887, "timestamp": "2025-08-15T22:52:02.018140"}]}, "statistics": {"total_samples": 3, "data_categories": 4, "sample_count_per_category": {"deviation_range": 3, "recognition_accuracy": 3, "detection_range": 3, "detection_probability": 3}}}], "jammers": [{"device_info": {"device_id": 0, "model": "多目标_烟幕干扰1", "device_type": "optical_jammer", "jammer_type": "smoke_screen", "generation_timestamp": "2025-08-15T22:51:40.857051"}, "jamming_data": {"effectiveness": [{"sample_id": 0, "target_distance_m": 1467.6, "base_effectiveness": 0.0, "weather_factor": 0.98, "atmospheric_factor": 0.996, "target_vulnerability": 0.913, "final_effectiveness": 0.0, "jammer_type": "smoke_screen", "timestamp": "2025-08-15T22:51:40.856551"}, {"sample_id": 1, "target_distance_m": 1752.4, "base_effectiveness": 0.0, "weather_factor": 0.95, "atmospheric_factor": 0.968, "target_vulnerability": 0.799, "final_effectiveness": 0.0, "jammer_type": "smoke_screen", "timestamp": "2025-08-15T22:51:40.856551"}, {"sample_id": 2, "target_distance_m": 1795.9, "base_effectiveness": 0.0, "weather_factor": 0.956, "atmospheric_factor": 0.95, "target_vulnerability": 0.31, "final_effectiveness": 0.0, "jammer_type": "smoke_screen", "timestamp": "2025-08-15T22:51:40.856551"}], "power_consumption": [{"sample_id": 0, "base_power_w": 1000, "mode_factor": 0.686, "temp_factor": 1.006, "efficiency": 0.877, "actual_power_w": 690.5, "total_power_w": 787.8, "work_mode": "area_denial", "timestamp": "2025-08-15T22:51:40.856551"}, {"sample_id": 1, "base_power_w": 1000, "mode_factor": 0.482, "temp_factor": 1.006, "efficiency": 0.823, "actual_power_w": 485.3, "total_power_w": 589.7, "work_mode": "area_denial", "timestamp": "2025-08-15T22:51:40.857051"}, {"sample_id": 2, "base_power_w": 1000, "mode_factor": 0.338, "temp_factor": 1.006, "efficiency": 0.774, "actual_power_w": 339.7, "total_power_w": 439.0, "work_mode": "area_denial", "timestamp": "2025-08-15T22:51:40.857051"}], "coverage": [{"sample_id": 0, "base_range_m": 4000, "power_factor": 1.0, "weather_factor": 0.993, "terrain_factor": 1.061, "actual_range_m": 4213.8, "coverage_angle_deg": 161.1, "jammer_type": "smoke_screen", "timestamp": "2025-08-15T22:51:40.857051"}, {"sample_id": 1, "base_range_m": 4000, "power_factor": 1.0, "weather_factor": 0.979, "terrain_factor": 0.927, "actual_range_m": 3628.6, "coverage_angle_deg": 217.5, "jammer_type": "smoke_screen", "timestamp": "2025-08-15T22:51:40.857051"}, {"sample_id": 2, "base_range_m": 4000, "power_factor": 1.0, "weather_factor": 0.987, "terrain_factor": 0.974, "actual_range_m": 3846.9, "coverage_angle_deg": 294.7, "jammer_type": "smoke_screen", "timestamp": "2025-08-15T22:51:40.857051"}], "duration": [{"sample_id": 0, "base_duration_s": 300, "wind_factor": 0.5, "power_factor": 0.858, "actual_duration_s": 128.6, "wind_speed_ms": 5.0, "jammer_type": "smoke_screen", "timestamp": "2025-08-15T22:51:40.857051"}, {"sample_id": 1, "base_duration_s": 300, "wind_factor": 0.5, "power_factor": 1.082, "actual_duration_s": 162.3, "wind_speed_ms": 5.0, "jammer_type": "smoke_screen", "timestamp": "2025-08-15T22:51:40.857051"}, {"sample_id": 2, "base_duration_s": 300, "wind_factor": 0.5, "power_factor": 1.082, "actual_duration_s": 162.3, "wind_speed_ms": 5.0, "jammer_type": "smoke_screen", "timestamp": "2025-08-15T22:51:40.857051"}]}, "performance_summary": {"max_power_w": 1000, "max_range_m": 4000, "frequency_hz": 1000, "effectiveness_range": [0.1, 0.98]}, "environmental_factors": {"weather_condition": "clear_weather", "temperature_k": 291.15, "humidity": 0.6, "wind_speed_ms": 5.0}, "statistics": {"total_samples": 3, "data_categories": 4, "sample_count_per_category": {"effectiveness": 3, "power_consumption": 3, "coverage": 3, "duration": 3}}}, {"device_info": {"device_id": 1, "model": "多目标_激光干扰2", "device_type": "optical_jammer", "jammer_type": "laser_dazzler", "generation_timestamp": "2025-08-15T22:51:40.857555"}, "jamming_data": {"effectiveness": [{"sample_id": 0, "target_distance_m": 4271.0, "base_effectiveness": 0.3, "weather_factor": 0.991, "atmospheric_factor": 0.294, "target_vulnerability": 0.753, "final_effectiveness": 0.066, "jammer_type": "laser_dazzler", "timestamp": "2025-08-15T22:51:40.857555"}, {"sample_id": 1, "target_distance_m": 1045.4, "base_effectiveness": 0.3, "weather_factor": 1.0, "atmospheric_factor": 0.741, "target_vulnerability": 0.782, "final_effectiveness": 0.174, "jammer_type": "laser_dazzler", "timestamp": "2025-08-15T22:51:40.857555"}, {"sample_id": 2, "target_distance_m": 11189.8, "base_effectiveness": 0.3, "weather_factor": 0.996, "atmospheric_factor": 0.041, "target_vulnerability": 0.983, "final_effectiveness": 0.012, "jammer_type": "laser_dazzler", "timestamp": "2025-08-15T22:51:40.857555"}], "power_consumption": [{"sample_id": 0, "base_power_w": 3000, "mode_factor": 0.499, "temp_factor": 1.006, "efficiency": 0.753, "actual_power_w": 1504.5, "total_power_w": 1999.2, "work_mode": "active_dazzling", "timestamp": "2025-08-15T22:51:40.857555"}, {"sample_id": 1, "base_power_w": 3000, "mode_factor": 0.515, "temp_factor": 1.006, "efficiency": 0.853, "actual_power_w": 1554.5, "total_power_w": 1822.6, "work_mode": "active_dazzling", "timestamp": "2025-08-15T22:51:40.857555"}, {"sample_id": 2, "base_power_w": 3000, "mode_factor": 0.6, "temp_factor": 1.006, "efficiency": 0.716, "actual_power_w": 1810.4, "total_power_w": 2527.8, "work_mode": "active_dazzling", "timestamp": "2025-08-15T22:51:40.857555"}], "coverage": [{"sample_id": 0, "base_range_m": 12000, "power_factor": 1.732, "weather_factor": 0.985, "terrain_factor": 0.866, "actual_range_m": 17725.5, "coverage_angle_deg": 5.3, "jammer_type": "laser_dazzler", "timestamp": "2025-08-15T22:51:40.857555"}, {"sample_id": 1, "base_range_m": 12000, "power_factor": 1.732, "weather_factor": 0.966, "terrain_factor": 0.99, "actual_range_m": 19876.4, "coverage_angle_deg": 5.7, "jammer_type": "laser_dazzler", "timestamp": "2025-08-15T22:51:40.857555"}, {"sample_id": 2, "base_range_m": 12000, "power_factor": 1.732, "weather_factor": 0.969, "terrain_factor": 0.847, "actual_range_m": 17069.7, "coverage_angle_deg": 14.0, "jammer_type": "laser_dazzler", "timestamp": "2025-08-15T22:51:40.857555"}], "duration": [{"sample_id": 0, "base_duration_s": 472.72660715471966, "wind_factor": 0.975, "power_factor": 0.81, "actual_duration_s": 373.5, "wind_speed_ms": 5.0, "jammer_type": "laser_dazzler", "timestamp": "2025-08-15T22:51:40.857555"}, {"sample_id": 1, "base_duration_s": 549.5267045978562, "wind_factor": 1.057, "power_factor": 1.048, "actual_duration_s": 608.7, "wind_speed_ms": 5.0, "jammer_type": "laser_dazzler", "timestamp": "2025-08-15T22:51:40.857555"}, {"sample_id": 2, "base_duration_s": 174.2580283454251, "wind_factor": 0.958, "power_factor": 1.065, "actual_duration_s": 177.8, "wind_speed_ms": 5.0, "jammer_type": "laser_dazzler", "timestamp": "2025-08-15T22:51:40.857555"}]}, "performance_summary": {"max_power_w": 3000, "max_range_m": 12000, "frequency_hz": 1200, "effectiveness_range": [0.1, 0.98]}, "environmental_factors": {"weather_condition": "clear_weather", "temperature_k": 291.15, "humidity": 0.6, "wind_speed_ms": 5.0}, "statistics": {"total_samples": 3, "data_categories": 4, "sample_count_per_category": {"effectiveness": 3, "power_consumption": 3, "coverage": 3, "duration": 3}}}], "recons": [{"device_info": {"device_id": 0, "model": "多目标_侦察系统1", "device_type": "optical_recon", "recon_type": "infrared_detector", "detection_mode": "infrared_warning", "work_mode": "multi_target_tracking", "generation_timestamp": "2025-08-15T22:51:40.859550"}, "reconnaissance_data": {"initial_screening": [{"sample_id": 0, "target_present": false, "signal_strength": 0.268, "noise_level": 0.099, "snr_db": 8.6, "detected": true, "result_type": "false_alarm", "recon_type": "infrared_detector", "timestamp": "2025-08-15T22:51:40.858551"}, {"sample_id": 1, "target_present": true, "signal_strength": 0.506, "noise_level": 0.061, "snr_db": 18.4, "detected": true, "result_type": "hit", "recon_type": "infrared_detector", "timestamp": "2025-08-15T22:51:40.858551"}, {"sample_id": 2, "target_present": false, "signal_strength": 0.392, "noise_level": 0.154, "snr_db": 8.1, "detected": true, "result_type": "false_alarm", "recon_type": "infrared_detector", "timestamp": "2025-08-15T22:51:40.859050"}], "feature_extraction": [{"sample_id": 0, "extracted_features": ["spectral", "temporal"], "feature_quality": {"spectral": 0.932, "temporal": 0.675}, "overall_confidence": 0.804, "processing_time_s": 1.11, "recon_type": "infrared_detector", "timestamp": "2025-08-15T22:51:40.859050"}, {"sample_id": 1, "extracted_features": ["spatial", "temporal", "polarization"], "feature_quality": {"spatial": 0.888, "temporal": 0.715, "polarization": 0.543}, "overall_confidence": 0.715, "processing_time_s": 1.779, "recon_type": "infrared_detector", "timestamp": "2025-08-15T22:51:40.859050"}, {"sample_id": 2, "extracted_features": ["spatial"], "feature_quality": {"spatial": 0.726}, "overall_confidence": 0.726, "processing_time_s": 0.883, "recon_type": "infrared_detector", "timestamp": "2025-08-15T22:51:40.859050"}], "target_tracking": [{"sample_id": 0, "target_speed_ms": 17.2, "target_direction_deg": 202.9, "tracking_accuracy": 0.854, "position_error_m": 6.1, "velocity_error_ms": 1.46, "track_state": "tracking", "track_duration_s": 131.6, "speed_factor": 0.993, "distance_factor": 0.956, "timestamp": "2025-08-15T22:51:40.859050"}, {"sample_id": 1, "target_speed_ms": 291.9, "target_direction_deg": 16.8, "tracking_accuracy": 0.627, "position_error_m": 4.0, "velocity_error_ms": 4.75, "track_state": "tracking", "track_duration_s": 20.9, "speed_factor": 0.708, "distance_factor": 0.983, "timestamp": "2025-08-15T22:51:40.859050"}, {"sample_id": 2, "target_speed_ms": 83.7, "target_direction_deg": 334.1, "tracking_accuracy": 0.805, "position_error_m": 4.0, "velocity_error_ms": 3.61, "track_state": "tracking", "track_duration_s": 281.6, "speed_factor": 0.924, "distance_factor": 0.968, "timestamp": "2025-08-15T22:51:40.859050"}], "recognition_accuracy": [{"sample_id": 0, "true_type": "unknown", "recognized_type": "missile", "correct_recognition": false, "confidence": 0.65, "recognition_accuracy": 0.444, "distance_m": 18100.2, "distance_factor": 0.749, "weather_factor": 0.913, "target_size_factor": 0.634, "timestamp": "2025-08-15T22:51:40.859050"}, {"sample_id": 1, "true_type": "aircraft", "recognized_type": "aircraft", "correct_recognition": true, "confidence": 0.925, "recognition_accuracy": 0.497, "distance_m": 27491.6, "distance_factor": 0.61, "weather_factor": 0.88, "target_size_factor": 0.791, "timestamp": "2025-08-15T22:51:40.859550"}, {"sample_id": 2, "true_type": "ship", "recognized_type": "missile", "correct_recognition": false, "confidence": 0.586, "recognition_accuracy": 0.345, "distance_m": 20451.8, "distance_factor": 0.714, "weather_factor": 0.825, "target_size_factor": 1.126, "timestamp": "2025-08-15T22:51:40.859550"}], "detection_range": [{"sample_id": 0, "base_range_m": 35000, "weather_factor": 0.922, "target_signature": 1.046, "sensor_performance": 0.943, "actual_range_m": 31856.2, "weather_condition": "clear_weather", "recon_type": "infrared_detector", "timestamp": "2025-08-15T22:51:40.859550"}, {"sample_id": 1, "base_range_m": 35000, "weather_factor": 0.997, "target_signature": 1.228, "sensor_performance": 0.808, "actual_range_m": 34633.0, "weather_condition": "clear_weather", "recon_type": "infrared_detector", "timestamp": "2025-08-15T22:51:40.859550"}, {"sample_id": 2, "base_range_m": 35000, "weather_factor": 0.922, "target_signature": 0.444, "sensor_performance": 0.853, "actual_range_m": 12227.9, "weather_condition": "clear_weather", "recon_type": "infrared_detector", "timestamp": "2025-08-15T22:51:40.859550"}], "discovery_probability": [{"sample_id": 0, "distance_m": 22832.0, "base_probability": 0.636, "weather_factor": 0.897, "atmospheric_factor": 0.913, "target_visibility": 0.855, "sensor_condition": 0.965, "discovery_probability": 0.43, "recon_type": "infrared_detector", "timestamp": "2025-08-15T22:51:40.859550"}, {"sample_id": 1, "distance_m": 19665.0, "base_probability": 0.704, "weather_factor": 0.986, "atmospheric_factor": 0.839, "target_visibility": 1.08, "sensor_condition": 0.996, "discovery_probability": 0.626, "recon_type": "infrared_detector", "timestamp": "2025-08-15T22:51:40.859550"}, {"sample_id": 2, "distance_m": 20318.2, "base_probability": 0.69, "weather_factor": 0.799, "atmospheric_factor": 0.852, "target_visibility": 0.932, "sensor_condition": 0.918, "discovery_probability": 0.402, "recon_type": "infrared_detector", "timestamp": "2025-08-15T22:51:40.859550"}]}, "performance_summary": {"max_detection_range_m": 35000, "resolution_mrad": 0.02, "spectral_coverage_m": [3e-06, 1.2e-05], "detection_threshold": 0.7, "tracking_accuracy": 0.9, "false_alarm_rate": 0.05}, "sensor_specifications": {"detector_type": "mercury_cadmium_telluride", "image_resolution": [640, 480], "spectral_range": [1e-06, 1.2e-05], "quantum_efficiency": 0.7}, "environmental_factors": {"weather_condition": "clear_weather", "temperature_k": 291.15, "humidity": 0.6, "atmospheric_visibility_m": 25000}, "statistics": {"total_samples": 3, "data_categories": 6, "sample_count_per_category": {"initial_screening": 3, "feature_extraction": 3, "target_tracking": 3, "recognition_accuracy": 3, "detection_range": 3, "discovery_probability": 3}}}, {"device_info": {"device_id": 1, "model": "多目标_侦察系统2", "device_type": "optical_recon", "recon_type": "laser_warning", "detection_mode": "laser_warning", "work_mode": "coordinated_surveillance", "generation_timestamp": "2025-08-15T22:51:40.929551"}, "reconnaissance_data": {"initial_screening": [{"sample_id": 0, "target_present": false, "signal_strength": 0.382, "noise_level": 0.122, "snr_db": 9.9, "detected": true, "result_type": "false_alarm", "recon_type": "laser_warning", "timestamp": "2025-08-15T22:51:40.928123"}, {"sample_id": 1, "target_present": false, "signal_strength": 0.097, "noise_level": 0.193, "snr_db": -6.0, "detected": false, "result_type": "correct_rejection", "recon_type": "laser_warning", "timestamp": "2025-08-15T22:51:40.928552"}, {"sample_id": 2, "target_present": true, "signal_strength": 0.629, "noise_level": 0.187, "snr_db": 10.5, "detected": true, "result_type": "hit", "recon_type": "laser_warning", "timestamp": "2025-08-15T22:51:40.929051"}], "feature_extraction": [{"sample_id": 0, "extracted_features": ["polarization", "spatial", "spectral"], "feature_quality": {"polarization": 0.687, "spatial": 0.745, "spectral": 0.754}, "overall_confidence": 0.729, "processing_time_s": 0.435, "recon_type": "laser_warning", "timestamp": "2025-08-15T22:51:40.929051"}, {"sample_id": 1, "extracted_features": ["spatial"], "feature_quality": {"spatial": 0.85}, "overall_confidence": 0.85, "processing_time_s": 1.861, "recon_type": "laser_warning", "timestamp": "2025-08-15T22:51:40.929051"}, {"sample_id": 2, "extracted_features": ["temporal", "polarization"], "feature_quality": {"temporal": 0.708, "polarization": 0.434}, "overall_confidence": 0.571, "processing_time_s": 0.804, "recon_type": "laser_warning", "timestamp": "2025-08-15T22:51:40.929051"}], "target_tracking": [{"sample_id": 0, "target_speed_ms": 82.4, "target_direction_deg": 185.8, "tracking_accuracy": 0.735, "position_error_m": 4.2, "velocity_error_ms": 4.68, "track_state": "tracking", "track_duration_s": 238.5, "speed_factor": 0.925, "distance_factor": 0.883, "timestamp": "2025-08-15T22:51:40.929051"}, {"sample_id": 1, "target_speed_ms": 218.1, "target_direction_deg": 246.3, "tracking_accuracy": 0.652, "position_error_m": 7.0, "velocity_error_ms": 3.11, "track_state": "tracking", "track_duration_s": 148.8, "speed_factor": 0.785, "distance_factor": 0.923, "timestamp": "2025-08-15T22:51:40.929051"}, {"sample_id": 2, "target_speed_ms": 152.8, "target_direction_deg": 60.3, "tracking_accuracy": 0.76, "position_error_m": 19.2, "velocity_error_ms": 3.07, "track_state": "coasting", "track_duration_s": 57.6, "speed_factor": 0.852, "distance_factor": 0.99, "timestamp": "2025-08-15T22:51:40.929051"}], "recognition_accuracy": [{"sample_id": 0, "true_type": "missile", "recognized_type": "unknown", "correct_recognition": false, "confidence": 0.442, "recognition_accuracy": 0.379, "distance_m": 6403.3, "distance_factor": 0.907, "weather_factor": 0.947, "target_size_factor": 1.355, "timestamp": "2025-08-15T22:51:40.929551"}, {"sample_id": 1, "true_type": "missile", "recognized_type": "missile", "correct_recognition": true, "confidence": 0.892, "recognition_accuracy": 0.434, "distance_m": 20375.7, "distance_factor": 0.666, "weather_factor": 0.73, "target_size_factor": 0.598, "timestamp": "2025-08-15T22:51:40.929551"}, {"sample_id": 2, "true_type": "vehicle", "recognized_type": "ship", "correct_recognition": false, "confidence": 0.44, "recognition_accuracy": 0.302, "distance_m": 11247.5, "distance_factor": 0.823, "weather_factor": 0.832, "target_size_factor": 0.641, "timestamp": "2025-08-15T22:51:40.929551"}], "detection_range": [{"sample_id": 0, "base_range_m": 30000, "weather_factor": 0.971, "target_signature": 1.092, "sensor_performance": 0.82, "actual_range_m": 26089.5, "weather_condition": "clear_weather", "recon_type": "laser_warning", "timestamp": "2025-08-15T22:51:40.929551"}, {"sample_id": 1, "base_range_m": 30000, "weather_factor": 0.928, "target_signature": 0.868, "sensor_performance": 1.047, "actual_range_m": 25290.7, "weather_condition": "clear_weather", "recon_type": "laser_warning", "timestamp": "2025-08-15T22:51:40.929551"}, {"sample_id": 2, "base_range_m": 30000, "weather_factor": 0.994, "target_signature": 0.928, "sensor_performance": 0.914, "actual_range_m": 25303.1, "weather_condition": "clear_weather", "recon_type": "laser_warning", "timestamp": "2025-08-15T22:51:40.929551"}], "discovery_probability": [{"sample_id": 0, "distance_m": 18628.0, "base_probability": 0.659, "weather_factor": 0.742, "atmospheric_factor": 0.895, "target_visibility": 0.616, "sensor_condition": 0.904, "discovery_probability": 0.244, "recon_type": "laser_warning", "timestamp": "2025-08-15T22:51:40.929551"}, {"sample_id": 1, "distance_m": 801.0, "base_probability": 0.98, "weather_factor": 0.99, "atmospheric_factor": 0.81, "target_visibility": 0.814, "sensor_condition": 0.949, "discovery_probability": 0.607, "recon_type": "laser_warning", "timestamp": "2025-08-15T22:51:40.929551"}, {"sample_id": 2, "distance_m": 34611.3, "base_probability": 0.147, "weather_factor": 0.739, "atmospheric_factor": 0.839, "target_visibility": 0.696, "sensor_condition": 0.995, "discovery_probability": 0.063, "recon_type": "laser_warning", "timestamp": "2025-08-15T22:51:40.929551"}]}, "performance_summary": {"max_detection_range_m": 30000, "resolution_mrad": 0.025, "spectral_coverage_m": [4e-07, 1.7e-06], "detection_threshold": 0.7, "tracking_accuracy": 0.9, "false_alarm_rate": 0.05}, "sensor_specifications": {"detector_type": "indium_gallium_arsenide", "image_resolution": [640, 480], "spectral_range": [9e-07, 1.7e-06], "quantum_efficiency": 0.85}, "environmental_factors": {"weather_condition": "clear_weather", "temperature_k": 291.15, "humidity": 0.6, "atmospheric_visibility_m": 25000}, "statistics": {"total_samples": 3, "data_categories": 6, "sample_count_per_category": {"initial_screening": 3, "feature_extraction": 3, "target_tracking": 3, "recognition_accuracy": 3, "detection_range": 3, "discovery_probability": 3}}}], "analysis": [{"analysis_type": "interference_analysis", "analysis_data": [{"target_model": "多目标_红外目标A", "jammer_model": "多目标_烟幕干扰1", "distance": 1000.000000017, "interference_ratio": 9.99998999967e-05, "timestamp": "2025-08-15T22:52:02.329139"}, {"target_model": "多目标_红外目标A", "jammer_model": "多目标_激光干扰2", "distance": 700.0000000105714, "interference_ratio": 0.006122436484638579, "timestamp": "2025-08-15T22:52:02.329139"}, {"target_model": "多目标_红外目标B", "jammer_model": "多目标_烟幕干扰1", "distance": 700.0000001664285, "interference_ratio": 0.00020408121606374085, "timestamp": "2025-08-15T22:52:02.329139"}, {"target_model": "多目标_红外目标B", "jammer_model": "多目标_激光干扰2", "distance": 400.00000006625, "interference_ratio": 0.018749882807021556, "timestamp": "2025-08-15T22:52:02.329139"}, {"target_model": "多目标_激光目标C", "jammer_model": "多目标_烟幕干扰1", "distance": 1300.0000001261537, "interference_ratio": 5.917156260889298e-05, "timestamp": "2025-08-15T22:52:02.329139"}, {"target_model": "多目标_激光目标C", "jammer_model": "多目标_激光干扰2", "distance": 1000.0000001039999, "interference_ratio": 0.002999996999379002, "timestamp": "2025-08-15T22:52:02.329139"}, {"target_model": "多目标_电视目标D", "jammer_model": "多目标_烟幕干扰1", "distance": 500.000000113, "interference_ratio": 0.00039999839982560143, "timestamp": "2025-08-15T22:52:02.329139"}, {"target_model": "多目标_电视目标D", "jammer_model": "多目标_激光干扰2", "distance": 200.0000004325, "interference_ratio": 0.07499812472251505, "timestamp": "2025-08-15T22:52:02.329139"}], "summary": {"total_interactions": 8, "target_count": 4, "jammer_count": 2, "generation_timestamp": "2025-08-15T22:52:02.329139"}}]}}, "performance_metrics": {"processing_speed": 0.1396025032776341, "files_per_second": 1.2098883617394955, "data_entries_per_second": 0.4188075098329023, "thread_count": 8, "thread_utilization": 0.8, "memory_usage": {"rss": 77045760, "vms": 571101184, "percent": 0.22932469060063806}, "execution_efficiency": {"data_per_second": 0.1396025032776341, "time_per_data": 7.163195333333333, "total_operations": 3, "successful_operations": 26, "data_entries_generated": 9}, "data_statistics": {"total_files": 26, "total_data_entries": 9, "data_breakdown": {"targets": 4, "jammers": 2, "recons": 2, "analysis": 1}}}, "server_info": {"ipv4_address": "**************", "port": 8587, "base_url": "http://**************:8587", "static_files_url": "http://**************:8587/simulation_results", "session_list_url": "http://**************:8587/api/simulation_results"}, "error_info": null}
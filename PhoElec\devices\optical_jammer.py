"""
光电干扰设备仿真模块
实现光电干扰设备的建模和数据生成
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
from datetime import datetime

from ..core.config_manager import OpticalJammerConfig, SystemConfig
from ..physics.radiation import LaserRadiation
from ..physics.atmosphere import AtmosphericTransmission
from ..physics.constants import JAMMING_PARAMS, WAVELENGTH_RANGES
from ..utils.logger import LoggerMixin, log_execution_time


logger = logging.getLogger(__name__)


class OpticalJammerSimulator(LoggerMixin):
    """光电干扰仿真器"""
    
    def __init__(
        self,
        config: OpticalJammerConfig,
        system_config: SystemConfig,
        environment: Dict[str, Any]
    ):
        """
        初始化光电干扰仿真器
        
        Args:
            config: 光电干扰配置
            system_config: 系统配置
            environment: 环境参数
        """
        self.config = config
        self.system_config = system_config
        self.environment = environment
        
        # 初始化物理模型
        self.atmosphere_model = AtmosphericTransmission(
            environment.get('weather_condition', 'clear_weather')
        )
        
        # 性能参数
        self.jamming_power = config.performance_params.get('jamming_power', 1000)  # W
        self.jamming_frequency = config.performance_params.get('jamming_frequency', 1000)  # Hz
        self.coverage_range = config.performance_params.get('coverage_range', 5000)  # m

        # 根据干扰设备类型初始化相应模型
        self.jammer_type = self._determine_jammer_type()
        self._initialize_jammer_models()
        
        self.logger.info(f"初始化光电干扰仿真器: {config.model} ({self.jammer_type})")
    
    def _determine_jammer_type(self) -> str:
        """确定干扰设备类型"""
        model_lower = self.config.model.lower()
        
        if 'smoke' in model_lower or '烟幕' in model_lower:
            return 'smoke_screen'
        elif 'decoy' in model_lower or 'flare' in model_lower or '诱饵' in model_lower:
            return 'infrared_decoy'
        elif 'laser' in model_lower or 'dazzler' in model_lower or '激光' in model_lower:
            return 'laser_dazzler'
        elif 'chaff' in model_lower or '箔条' in model_lower:
            return 'chaff'
        else:
            return 'generic_jammer'
    
    def _initialize_jammer_models(self):
        """初始化干扰器模型"""
        if self.jammer_type == 'laser_dazzler':
            laser_config = {
                'wavelength': self.config.performance_params.get('wavelength', 0.532e-6),
                'power': self.jamming_power,
                'beam_divergence': self.config.performance_params.get('beam_divergence', 10e-3),
                'pulse_duration': self.config.performance_params.get('pulse_duration', 1e-6),
                'repetition_rate': self.jamming_frequency
            }
            self.laser_model = LaserRadiation(laser_config)
        
        # 获取干扰参数
        self.jammer_params = JAMMING_PARAMS.get(self.jammer_type, {})
    
    @log_execution_time
    def generate_jamming_data(
        self,
        count: int,
        output_manager,
        device_id: int
    ) -> List[str]:
        """
        生成干扰数据

        Args:
            count: 生成数量
            output_manager: 输出管理器
            device_id: 设备ID

        Returns:
            生成的数据文件路径列表
        """
        self.logger.info(f"开始生成 {count} 条干扰数据")

        # 生成不同类型的干扰数据
        jamming_effectiveness_data = self._generate_jamming_effectiveness_data(count)
        power_consumption_data = self._generate_power_consumption_data(count)
        coverage_data = self._generate_coverage_data(count)
        duration_data = self._generate_duration_data(count)

        # 构建综合干扰数据
        comprehensive_data = {
            'device_info': {
                'device_id': device_id,
                'model': self.config.model,
                'device_type': 'optical_jammer',
                'jammer_type': self.jammer_type,
                'generation_timestamp': datetime.now().isoformat()
            },
            'jamming_data': {
                'effectiveness': jamming_effectiveness_data,
                'power_consumption': power_consumption_data,
                'coverage': coverage_data,
                'duration': duration_data
            },
            'performance_summary': {
                'max_power_w': self.jamming_power,
                'max_range_m': self.coverage_range,
                'frequency_hz': self.jamming_frequency,
                'effectiveness_range': [0.1, 0.98]
            },
            'environmental_factors': {
                'weather_condition': self.environment.get('weather_condition', 'clear'),
                'temperature_k': self.environment.get('temperature', 288.15),
                'humidity': self.environment.get('humidity', 0.5),
                'wind_speed_ms': self.environment.get('wind_speed', 5)
            },
            'statistics': {
                'total_samples': count,
                'data_categories': 4,
                'sample_count_per_category': {
                    'effectiveness': len(jamming_effectiveness_data),
                    'power_consumption': len(power_consumption_data),
                    'coverage': len(coverage_data),
                    'duration': len(duration_data)
                }
            }
        }

        # 保存综合数据文件
        file_paths = []
        comprehensive_path = output_manager.save_json_data(
            comprehensive_data, 'data', f'jammer_{device_id}_comprehensive'
        )
        file_paths.append(comprehensive_path)

        self.logger.info(f"干扰数据生成完成，共 {len(file_paths)} 个文件")
        return file_paths
    
    def _generate_jamming_effectiveness_data(self, count: int) -> List[Dict[str, Any]]:
        """生成干扰效果数据"""
        data = []
        
        for i in range(count):
            # 目标距离
            target_distance = np.random.uniform(500, self.coverage_range)
            
            # 基础干扰效果
            base_effectiveness = self._calculate_base_effectiveness(target_distance)
            
            # 环境影响
            weather_factor = self._get_weather_factor()
            atmospheric_factor = self._calculate_atmospheric_factor(target_distance)
            
            # 目标特性影响
            target_vulnerability = np.random.uniform(0.3, 1.0)
            
            # 计算最终干扰效果
            final_effectiveness = (base_effectiveness * weather_factor * 
                                 atmospheric_factor * target_vulnerability)
            final_effectiveness = max(0.0, min(1.0, final_effectiveness))
            
            data.append({
                'sample_id': i,
                'target_distance_m': round(target_distance, 1),
                'base_effectiveness': round(base_effectiveness, 3),
                'weather_factor': round(weather_factor, 3),
                'atmospheric_factor': round(atmospheric_factor, 3),
                'target_vulnerability': round(target_vulnerability, 3),
                'final_effectiveness': round(final_effectiveness, 3),
                'jammer_type': self.jammer_type,
                'timestamp': datetime.now().isoformat()
            })
        
        return data
    
    def _calculate_base_effectiveness(self, distance: float) -> float:
        """计算基础干扰效果"""
        if self.jammer_type == 'smoke_screen':
            # 烟幕干扰效果主要取决于密度和覆盖范围
            coverage_radius = self.jammer_params.get('coverage_radius', 100)
            if distance <= coverage_radius:
                effectiveness = 0.9 * (1 - distance / coverage_radius * 0.5)
            else:
                effectiveness = 0.1 * np.exp(-(distance - coverage_radius) / coverage_radius)
        
        elif self.jammer_type == 'infrared_decoy':
            # 红外诱饵效果基于辐射强度和距离
            radiant_intensity = self.jammer_params.get('radiant_intensity', 1000)
            effectiveness = min(0.95, radiant_intensity / (distance**2 + 1000))
        
        elif self.jammer_type == 'laser_dazzler':
            # 激光致盲效果基于功率密度
            if hasattr(self, 'laser_model'):
                power_density = self.laser_model.average_power_density(distance, 1.0)
                effectiveness = min(0.98, power_density / 10000)  # 简化模型
            else:
                effectiveness = max(0.1, self.jamming_power / (distance**2 + 100))
        
        else:
            # 通用干扰模型
            effectiveness = max(0.1, self.jamming_power / (distance**2 + 1000))
        
        return min(1.0, effectiveness)
    
    def _get_weather_factor(self) -> float:
        """获取天气影响因子"""
        weather_condition = self.environment.get('weather_condition', 'clear_weather')
        
        if self.jammer_type == 'smoke_screen':
            # 烟幕在不同天气条件下的效果
            if weather_condition == 'clear_weather':
                return np.random.uniform(0.9, 1.0)
            elif weather_condition == 'wind':
                return np.random.uniform(0.5, 0.7)  # 风会吹散烟幕
            elif weather_condition == 'rain':
                return np.random.uniform(0.6, 0.8)  # 雨水会稀释烟幕
            else:
                return np.random.uniform(0.8, 0.9)
        
        elif self.jammer_type == 'laser_dazzler':
            # 激光在不同天气条件下的传输
            if weather_condition == 'clear_weather':
                return np.random.uniform(0.95, 1.0)
            elif weather_condition == 'haze':
                return np.random.uniform(0.7, 0.8)
            elif weather_condition == 'fog':
                return np.random.uniform(0.3, 0.5)
            else:
                return np.random.uniform(0.8, 0.9)
        
        else:
            # 其他干扰设备的天气影响
            return np.random.uniform(0.8, 1.0)
    
    def _calculate_atmospheric_factor(self, distance: float) -> float:
        """计算大气影响因子"""
        if self.jammer_type == 'laser_dazzler':
            # 激光的大气传输
            wavelength = getattr(self, 'laser_model', None)
            if wavelength:
                transmission = self.atmosphere_model.total_atmospheric_transmission(
                    0.532e-6, distance, self.environment
                )
                return transmission
        
        # 其他类型的大气影响较小
        return np.random.uniform(0.9, 1.0)

    def _generate_power_consumption_data(self, count: int) -> List[Dict[str, Any]]:
        """生成功耗数据"""
        data = []

        for i in range(count):
            # 基础功耗
            base_power = self.jamming_power

            # 工作模式影响
            work_mode = self.config.work_mode
            if work_mode == 'continuous':
                mode_factor = 1.0
            elif work_mode == 'pulse':
                duty_cycle = np.random.uniform(0.1, 0.5)
                mode_factor = duty_cycle
            else:
                mode_factor = np.random.uniform(0.3, 0.8)

            # 环境温度影响
            ambient_temp = self.environment.get('temperature', 288.15)
            temp_factor = 1.0 + (ambient_temp - 288.15) * 0.002  # 温度系数

            # 计算实际功耗
            actual_power = base_power * mode_factor * temp_factor

            # 效率损失
            efficiency = np.random.uniform(0.7, 0.9)
            total_power = actual_power / efficiency

            data.append({
                'sample_id': i,
                'base_power_w': base_power,
                'mode_factor': round(mode_factor, 3),
                'temp_factor': round(temp_factor, 3),
                'efficiency': round(efficiency, 3),
                'actual_power_w': round(actual_power, 1),
                'total_power_w': round(total_power, 1),
                'work_mode': work_mode,
                'timestamp': datetime.now().isoformat()
            })

        return data

    def _generate_coverage_data(self, count: int) -> List[Dict[str, Any]]:
        """生成覆盖范围数据"""
        data = []

        for i in range(count):
            # 基础覆盖范围
            base_range = self.coverage_range

            # 功率影响
            power_factor = np.sqrt(self.jamming_power / 1000)  # 功率开方关系

            # 环境影响
            weather_factor = self._get_weather_factor()

            # 地形影响
            terrain_factor = np.random.uniform(0.8, 1.2)

            # 计算实际覆盖范围
            actual_range = base_range * power_factor * weather_factor * terrain_factor
            actual_range = max(100, actual_range)

            # 覆盖角度
            if self.jammer_type == 'laser_dazzler':
                coverage_angle = np.random.uniform(5, 15)  # 激光覆盖角度较小
            else:
                coverage_angle = np.random.uniform(30, 360)  # 其他类型覆盖角度较大

            data.append({
                'sample_id': i,
                'base_range_m': base_range,
                'power_factor': round(power_factor, 3),
                'weather_factor': round(weather_factor, 3),
                'terrain_factor': round(terrain_factor, 3),
                'actual_range_m': round(actual_range, 1),
                'coverage_angle_deg': round(coverage_angle, 1),
                'jammer_type': self.jammer_type,
                'timestamp': datetime.now().isoformat()
            })

        return data

    def _generate_duration_data(self, count: int) -> List[Dict[str, Any]]:
        """生成持续时间数据"""
        data = []

        for i in range(count):
            # 基础持续时间
            if self.jammer_type == 'smoke_screen':
                base_duration = self.jammer_params.get('duration', 300)
            elif self.jammer_type == 'infrared_decoy':
                base_duration = self.jammer_params.get('burn_time', 60)
            else:
                base_duration = np.random.uniform(60, 600)

            # 环境影响
            wind_speed = self.environment.get('wind_speed', 5)  # m/s
            if self.jammer_type == 'smoke_screen':
                wind_factor = max(0.3, 1.0 - wind_speed * 0.1)
            else:
                wind_factor = np.random.uniform(0.9, 1.1)

            # 功率影响
            power_factor = np.random.uniform(0.8, 1.2)

            # 计算实际持续时间
            actual_duration = base_duration * wind_factor * power_factor
            actual_duration = max(10, actual_duration)

            data.append({
                'sample_id': i,
                'base_duration_s': base_duration,
                'wind_factor': round(wind_factor, 3),
                'power_factor': round(power_factor, 3),
                'actual_duration_s': round(actual_duration, 1),
                'wind_speed_ms': wind_speed,
                'jammer_type': self.jammer_type,
                'timestamp': datetime.now().isoformat()
            })

        return data



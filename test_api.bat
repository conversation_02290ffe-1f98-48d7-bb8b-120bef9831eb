@echo off
setlocal enabledelayedexpansion

echo ========================================
echo  Optical Simulation API Test Script
echo ========================================
echo.

:: Configuration
set API_HOST=localhost
set API_PORT=8587
set BASE_URL=http://%API_HOST%:%API_PORT%
set HEALTH_URL=%BASE_URL%/health
set SIMULATION_URL=%BASE_URL%/run_simulation
set REQUEST_FILE=test_request.json

:: Check if curl is available
curl --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: curl is not available. Please install curl or use Windows 10/11.
    pause
    exit /b 1
)

:: Check if request file exists
if not exist "%REQUEST_FILE%" (
    echo ERROR: Request file "%REQUEST_FILE%" not found.
    echo Please ensure the JSON request file is in the same directory.
    pause
    exit /b 1
)

echo Step 1: Checking API health...
echo URL: %HEALTH_URL%
echo.

curl -s -w "HTTP Status: %%{http_code}\nResponse Time: %%{time_total}s\n" ^
     -H "Content-Type: application/json" ^
     "%HEALTH_URL%"

if %errorlevel% neq 0 (
    echo.
    echo ERROR: Failed to connect to API server.
    echo Please ensure the API server is running:
    echo   python http_api.py
    echo.
    pause
    exit /b 1
)

echo.
echo ========================================
echo Step 2: Sending simulation request...
echo URL: %SIMULATION_URL%
echo Request File: %REQUEST_FILE%
echo.

:: Get current timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "timestamp=%dt:~0,4%-%dt:~4,2%-%dt:~6,2% %dt:~8,2%:%dt:~10,2%:%dt:~12,2%"

echo Request Time: %timestamp%
echo.

:: Send POST request with JSON data
curl -X POST ^
     -H "Content-Type: application/json" ^
     -d @"%REQUEST_FILE%" ^
     -w "\n\nHTTP Status: %%{http_code}\nResponse Time: %%{time_total}s\nTotal Size: %%{size_download} bytes\n" ^
     "%SIMULATION_URL%"

set curl_exit_code=%errorlevel%

echo.
echo ========================================

if %curl_exit_code% equ 0 (
    echo Request completed successfully!
) else (
    echo Request failed with exit code: %curl_exit_code%
)

echo.
echo Additional API endpoints you can test:
echo   GET  %BASE_URL%/           - API information
echo   GET  %BASE_URL%/health     - Health check
echo   POST %BASE_URL%/run_simulation - Run simulation
echo.

pause

"""
光电目标设备仿真模块
实现光电目标设备的数据生成功能，包括图像生成和参数计算
"""

import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
from PIL import Image, ImageDraw, ImageFont
import cv2
from datetime import datetime
import os

from ..core.config_manager import OpticalTargetConfig, SystemConfig
from ..physics.radiation import TargetRadiation, BlackbodyRadiation
from ..physics.atmosphere import AtmosphericTransmission
from ..physics.detection import ImagingSensor
from ..physics.constants import WAVELENGTH_RANGES, TYPICAL_TEMPERATURES
from ..utils.logger import LoggerMixin, log_execution_time


logger = logging.getLogger(__name__)


class OpticalTargetSimulator(LoggerMixin):
    """光电目标仿真器"""
    
    def __init__(
        self,
        config: OpticalTargetConfig,
        system_config: SystemConfig,
        environment: Dict[str, Any]
    ):
        """
        初始化光电目标仿真器
        
        Args:
            config: 光电目标配置
            system_config: 系统配置
            environment: 环境参数
        """
        self.config = config
        self.system_config = system_config
        self.environment = environment
        
        # 初始化物理模型
        self.radiation_model = TargetRadiation(self._build_radiation_config())
        self.atmosphere_model = AtmosphericTransmission(
            environment.get('weather_condition', 'clear_weather')
        )
        
        # 初始化成像传感器
        self.imaging_sensor = ImagingSensor(self._build_sensor_config())
        
        # 性能参数
        self.detection_range = config.performance_params.get('detection_range', 10000)  # m
        self.resolution = config.performance_params.get('resolution', 0.1)  # mrad
        self.field_of_view = config.performance_params.get('field_of_view', 10)  # degrees
        
        self.logger.info(f"初始化光电目标仿真器: {config.model}")
    
    def _build_radiation_config(self) -> Dict[str, Any]:
        """构建辐射模型配置"""
        target_type = self.config.model.lower()
        
        if 'aircraft' in target_type:
            config = {
                'temperature': {
                    'engine': TYPICAL_TEMPERATURES['aircraft_engine'],
                    'body': TYPICAL_TEMPERATURES['aircraft_body'],
                    'exhaust': 900,
                    'background': TYPICAL_TEMPERATURES['background_day']
                },
                'component_areas': {
                    'engine': 2.0,
                    'body': 50.0,
                    'exhaust': 1.0
                }
            }
        elif 'vehicle' in target_type:
            config = {
                'temperature': {
                    'engine': TYPICAL_TEMPERATURES['vehicle_engine'],
                    'body': TYPICAL_TEMPERATURES['vehicle_body'],
                    'exhaust': 600,
                    'background': TYPICAL_TEMPERATURES['background_day']
                },
                'component_areas': {
                    'engine': 1.0,
                    'body': 20.0,
                    'exhaust': 0.5
                }
            }
        else:
            # 默认配置
            config = {
                'temperature': {
                    'engine': 400,
                    'body': 300,
                    'exhaust': 500,
                    'background': 280
                },
                'component_areas': {
                    'engine': 1.0,
                    'body': 10.0,
                    'exhaust': 0.3
                }
            }
        
        # 添加环境影响
        ambient_temp = self.environment.get('temperature', 288.15)
        for component in config['temperature']:
            if component != 'background':
                config['temperature'][component] += (ambient_temp - 288.15) * 0.1
        
        return config
    
    def _build_sensor_config(self) -> Dict[str, Any]:
        """构建传感器配置"""
        sensor_type = self.config.model.lower()
        
        if 'infrared' in sensor_type or 'ir' in sensor_type:
            config = {
                'resolution': self.system_config.image_resolution,
                'spectral_range': WAVELENGTH_RANGES['mid_infrared'],
                'quantum_efficiency': 0.7,
                'pixel_size': 15e-6,
                'focal_length': 0.1,
                'f_number': 2.0
            }
        elif 'laser' in sensor_type:
            config = {
                'resolution': self.system_config.image_resolution,
                'spectral_range': (1.0e-6, 1.1e-6),
                'quantum_efficiency': 0.8,
                'pixel_size': 10e-6,
                'focal_length': 0.15,
                'f_number': 1.8
            }
        else:  # TV/visible
            config = {
                'resolution': self.system_config.image_resolution,
                'spectral_range': WAVELENGTH_RANGES['visible'],
                'quantum_efficiency': 0.9,
                'pixel_size': 5e-6,
                'focal_length': 0.05,
                'f_number': 2.8
            }
        
        return config
    
    @log_execution_time
    def generate_static_images(
        self,
        count: int,
        output_manager,
        device_id: int
    ) -> List[str]:
        """
        生成静态图像
        
        Args:
            count: 生成数量
            output_manager: 输出管理器
            device_id: 设备ID
            
        Returns:
            生成的图像文件路径列表
        """
        self.logger.info(f"开始生成 {count} 张静态图像")
        
        image_paths = []
        
        for i in range(count):
            # 生成场景参数
            scene_params = self._generate_scene_parameters(i)
            
            # 生成图像
            image_array = self._generate_target_image(scene_params)
            
            # 添加中文标注
            annotated_image = self._add_chinese_annotations(image_array, scene_params)
            
            # 保存图像
            filename = f"target_{device_id}_static_{i:04d}"
            image_path = output_manager.save_image(
                annotated_image, 'images', filename, 'PNG'
            )
            image_paths.append(image_path)
            
            if (i + 1) % 100 == 0:
                self.logger.debug(f"已生成 {i + 1}/{count} 张静态图像")
        
        self.logger.info(f"静态图像生成完成，共 {len(image_paths)} 张")
        return image_paths
    
    @log_execution_time
    def generate_dynamic_images(
        self,
        duration: float,
        output_manager,
        device_id: int
    ) -> List[str]:
        """
        生成动态图像/视频
        
        Args:
            duration: 视频时长 (秒)
            output_manager: 输出管理器
            device_id: 设备ID
            
        Returns:
            生成的视频文件路径列表
        """
        self.logger.info(f"开始生成动态视频，时长: {duration}秒")
        
        fps = self.system_config.video_fps
        total_frames = int(duration * fps)
        
        # 创建视频写入器
        video_filename = f"target_{device_id}_dynamic.mp4"
        video_path = output_manager.get_output_path('videos', video_filename)
        
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        video_writer = cv2.VideoWriter(
            video_path, fourcc, fps, self.system_config.image_resolution
        )
        
        try:
            for frame_idx in range(total_frames):
                time_stamp = frame_idx / fps
                
                # 生成当前帧的场景参数
                scene_params = self._generate_dynamic_scene_parameters(time_stamp)
                
                # 生成图像
                image_array = self._generate_target_image(scene_params)
                
                # 添加时间戳和中文标注
                annotated_image = self._add_dynamic_annotations(
                    image_array, scene_params, time_stamp
                )
                
                # 转换为OpenCV格式并写入视频
                cv_image = cv2.cvtColor(np.array(annotated_image), cv2.COLOR_RGB2BGR)
                video_writer.write(cv_image)
                
                if (frame_idx + 1) % (fps * 10) == 0:  # 每10秒记录一次
                    self.logger.debug(f"已生成 {frame_idx + 1}/{total_frames} 帧")
            
        finally:
            video_writer.release()
        
        self.logger.info(f"动态视频生成完成: {video_path}")
        return [video_path]
    
    @log_execution_time
    def generate_parameter_data(
        self,
        count: int,
        output_manager,
        device_id: int
    ) -> List[str]:
        """
        生成参数数据

        Args:
            count: 生成数量
            output_manager: 输出管理器
            device_id: 设备ID

        Returns:
            生成的数据文件路径列表
        """
        self.logger.info(f"开始生成 {count} 条参数数据")

        # 生成各类参数数据
        deviation_data = self._generate_deviation_data(count)
        accuracy_data = self._generate_accuracy_data(count)
        detection_range_data = self._generate_detection_range_data(count)
        detection_probability_data = self._generate_detection_probability_data(count)

        # 构建综合参数数据
        comprehensive_data = {
            'device_info': {
                'device_id': device_id,
                'model': self.config.model,
                'device_type': 'optical_target',
                'generation_timestamp': datetime.now().isoformat()
            },
            'parameter_data': {
                'deviation_range': deviation_data,
                'recognition_accuracy': accuracy_data,
                'detection_range': detection_range_data,
                'detection_probability': detection_probability_data
            },
            'statistics': {
                'total_samples': count,
                'data_categories': 4,
                'sample_count_per_category': {
                    'deviation_range': len(deviation_data),
                    'recognition_accuracy': len(accuracy_data),
                    'detection_range': len(detection_range_data),
                    'detection_probability': len(detection_probability_data)
                }
            }
        }

        # 保存综合数据文件
        file_paths = []
        comprehensive_path = output_manager.save_json_data(
            comprehensive_data, 'data', f'target_{device_id}_parameters'
        )
        file_paths.append(comprehensive_path)

        self.logger.info(f"参数数据生成完成，共 {len(file_paths)} 个文件")
        return file_paths
    
    def _generate_scene_parameters(self, index: int) -> Dict[str, Any]:
        """生成场景参数"""
        np.random.seed(self.system_config.random_seed + index if self.system_config.random_seed else None)
        
        # 目标距离
        distance = np.random.uniform(1000, self.detection_range)
        
        # 目标角度
        azimuth = np.random.uniform(-self.field_of_view/2, self.field_of_view/2)
        elevation = np.random.uniform(-self.field_of_view/4, self.field_of_view/4)
        
        # 环境条件
        weather_factor = np.random.uniform(0.7, 1.0)
        
        # 目标状态
        target_state = np.random.choice(['normal', 'hot', 'cold'])
        
        return {
            'index': index,
            'distance': distance,
            'azimuth': azimuth,
            'elevation': elevation,
            'weather_factor': weather_factor,
            'target_state': target_state,
            'timestamp': datetime.now().isoformat()
        }
    
    def _generate_dynamic_scene_parameters(self, time: float) -> Dict[str, Any]:
        """生成动态场景参数"""
        # 目标运动
        distance = 5000 + 1000 * np.sin(0.1 * time)
        azimuth = 5 * np.sin(0.2 * time)
        elevation = 2 * np.cos(0.15 * time)
        
        # 温度变化
        self.radiation_model.apply_temperature_variation(time)
        
        return {
            'time': time,
            'distance': distance,
            'azimuth': azimuth,
            'elevation': elevation,
            'weather_factor': 0.9,
            'target_state': 'normal',
            'timestamp': datetime.now().isoformat()
        }
    
    def _generate_target_image(self, scene_params: Dict[str, Any]) -> np.ndarray:
        """生成目标图像"""
        width, height = self.system_config.image_resolution
        
        # 创建基础图像
        image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 背景
        bg_intensity = 50 + np.random.randint(-10, 10)
        image[:, :] = [bg_intensity, bg_intensity, bg_intensity]
        
        # 目标位置
        center_x = width // 2 + int(scene_params['azimuth'] * width / self.field_of_view)
        center_y = height // 2 + int(scene_params['elevation'] * height / self.field_of_view)
        
        # 目标大小（基于距离）
        base_size = 20
        target_size = max(5, int(base_size * 1000 / scene_params['distance']))
        
        # 目标亮度（基于辐射模型）
        wavelength_range = WAVELENGTH_RANGES.get('mid_infrared', (3e-6, 5e-6))
        radiant_intensity = self.radiation_model.get_total_radiant_intensity(wavelength_range)
        
        # 大气衰减
        transmission = self.atmosphere_model.total_atmospheric_transmission(
            4e-6, scene_params['distance'], self.environment
        )
        
        # 计算目标亮度
        received_intensity = radiant_intensity * transmission / (scene_params['distance']**2)
        target_brightness = min(255, int(100 + received_intensity * 1000))
        
        # 绘制目标
        if 0 <= center_x < width and 0 <= center_y < height:
            # 目标主体
            cv2.circle(image, (center_x, center_y), target_size, 
                      (target_brightness, target_brightness, target_brightness), -1)
            
            # 热点（发动机等）
            if scene_params['target_state'] == 'hot':
                hot_spot_brightness = min(255, target_brightness + 50)
                cv2.circle(image, (center_x-target_size//2, center_y), target_size//3,
                          (hot_spot_brightness, hot_spot_brightness, hot_spot_brightness), -1)
        
        # 添加噪声
        noise = np.random.normal(0, 5, image.shape).astype(np.int16)
        image = np.clip(image.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        return image

    def _add_chinese_annotations(self, image: np.ndarray, scene_params: Dict[str, Any]) -> Image.Image:
        """添加中文标注"""
        pil_image = Image.fromarray(image)
        draw = ImageDraw.Draw(pil_image)

        try:
            # 尝试加载中文字体
            font = ImageFont.truetype("simhei.ttf", 16)
        except:
            try:
                font = ImageFont.truetype("arial.ttf", 16)
            except:
                font = ImageFont.load_default()

        # 添加标注信息
        annotations = [
            f"目标型号: {self.config.model}",
            f"距离: {scene_params['distance']:.0f}m",
            f"方位角: {scene_params['azimuth']:.1f}°",
            f"俯仰角: {scene_params['elevation']:.1f}°",
            f"状态: {scene_params['target_state']}"
        ]

        y_offset = 10
        for annotation in annotations:
            draw.text((10, y_offset), annotation, fill=(255, 255, 0), font=font)
            y_offset += 20

        return pil_image

    def _add_dynamic_annotations(
        self,
        image: np.ndarray,
        scene_params: Dict[str, Any],
        time_stamp: float
    ) -> Image.Image:
        """添加动态标注"""
        pil_image = Image.fromarray(image)
        draw = ImageDraw.Draw(pil_image)

        try:
            font = ImageFont.truetype("simhei.ttf", 16)
        except:
            try:
                font = ImageFont.truetype("arial.ttf", 16)
            except:
                font = ImageFont.load_default()

        # 添加动态标注信息
        annotations = [
            f"时间: {time_stamp:.2f}s",
            f"目标型号: {self.config.model}",
            f"距离: {scene_params['distance']:.0f}m",
            f"方位角: {scene_params['azimuth']:.1f}°",
            f"俯仰角: {scene_params['elevation']:.1f}°"
        ]

        y_offset = 10
        for annotation in annotations:
            draw.text((10, y_offset), annotation, fill=(255, 255, 0), font=font)
            y_offset += 20

        return pil_image

    def _generate_deviation_data(self, count: int) -> List[Dict[str, Any]]:
        """生成偏离范围数据"""
        data = []

        for i in range(count):
            # 基础偏离范围
            base_deviation = self.resolution * 1000  # 转换为mrad

            # 环境影响因子
            weather_factor = np.random.uniform(0.8, 1.2)
            distance_factor = np.random.uniform(0.9, 1.1)

            # 计算偏离范围
            azimuth_deviation = base_deviation * weather_factor * np.random.uniform(0.5, 1.5)
            elevation_deviation = base_deviation * distance_factor * np.random.uniform(0.5, 1.5)

            data.append({
                'sample_id': i,
                'azimuth_deviation_mrad': round(azimuth_deviation, 3),
                'elevation_deviation_mrad': round(elevation_deviation, 3),
                'total_deviation_mrad': round(np.sqrt(azimuth_deviation**2 + elevation_deviation**2), 3),
                'weather_condition': self.environment.get('weather_condition', 'clear'),
                'timestamp': datetime.now().isoformat()
            })

        return data

    def _generate_accuracy_data(self, count: int) -> List[Dict[str, Any]]:
        """生成识别准确率数据"""
        data = []

        for i in range(count):
            # 基础识别准确率
            base_accuracy = 0.85

            # 影响因子
            distance = np.random.uniform(1000, self.detection_range)
            weather_factor = np.random.uniform(0.7, 1.0)
            target_size_factor = np.random.uniform(0.8, 1.2)

            # 距离影响
            distance_factor = max(0.3, 1.0 - (distance - 1000) / (self.detection_range - 1000) * 0.4)

            # 计算识别准确率
            accuracy = base_accuracy * distance_factor * weather_factor * target_size_factor
            accuracy = max(0.1, min(0.99, accuracy))

            data.append({
                'sample_id': i,
                'recognition_accuracy': round(accuracy, 3),
                'distance_m': round(distance, 1),
                'weather_factor': round(weather_factor, 3),
                'distance_factor': round(distance_factor, 3),
                'timestamp': datetime.now().isoformat()
            })

        return data

    def _generate_detection_range_data(self, count: int) -> List[Dict[str, Any]]:
        """生成探测距离数据"""
        data = []

        for i in range(count):
            # 基础探测距离
            base_range = self.detection_range

            # 环境影响
            weather_condition = self.environment.get('weather_condition', 'clear_weather')
            if weather_condition == 'clear_weather':
                weather_factor = np.random.uniform(0.9, 1.0)
            elif weather_condition == 'haze':
                weather_factor = np.random.uniform(0.6, 0.8)
            elif weather_condition == 'fog':
                weather_factor = np.random.uniform(0.3, 0.5)
            else:
                weather_factor = np.random.uniform(0.7, 0.9)

            # 目标特性影响
            target_contrast = np.random.uniform(0.5, 1.5)

            # 计算实际探测距离
            actual_range = base_range * weather_factor * target_contrast
            actual_range = max(500, min(base_range * 1.2, actual_range))

            data.append({
                'sample_id': i,
                'detection_range_m': round(actual_range, 1),
                'base_range_m': base_range,
                'weather_factor': round(weather_factor, 3),
                'target_contrast': round(target_contrast, 3),
                'weather_condition': weather_condition,
                'timestamp': datetime.now().isoformat()
            })

        return data

    def _generate_detection_probability_data(self, count: int) -> List[Dict[str, Any]]:
        """生成探测概率数据"""
        data = []

        for i in range(count):
            # 目标距离
            distance = np.random.uniform(500, self.detection_range * 1.5)

            # 基于距离的探测概率
            if distance <= self.detection_range * 0.5:
                base_probability = 0.95
            elif distance <= self.detection_range:
                base_probability = 0.8 - 0.3 * (distance - self.detection_range * 0.5) / (self.detection_range * 0.5)
            else:
                base_probability = 0.5 * np.exp(-(distance - self.detection_range) / (self.detection_range * 0.3))

            # 环境影响
            weather_factor = np.random.uniform(0.7, 1.0)
            noise_factor = np.random.uniform(0.8, 1.0)

            # 计算最终探测概率
            detection_probability = base_probability * weather_factor * noise_factor
            detection_probability = max(0.01, min(0.99, detection_probability))

            data.append({
                'sample_id': i,
                'detection_probability': round(detection_probability, 3),
                'distance_m': round(distance, 1),
                'base_probability': round(base_probability, 3),
                'weather_factor': round(weather_factor, 3),
                'noise_factor': round(noise_factor, 3),
                'timestamp': datetime.now().isoformat()
            })

        return data

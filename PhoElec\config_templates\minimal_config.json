{"simulation": {"scenario_name": "最小化配置测试", "duration": 10.0, "time_step": 0.5, "data_count": 10, "output_types": ["parameters"], "environment": {"weather_condition": "clear_weather"}}, "system": {"max_threads": 2, "image_resolution": [640, 480]}, "optical_targets": [{"model": "简单目标", "position": {"latitude": 40.0, "longitude": 116.0, "altitude": 500.0}, "observation_direction": {"azimuth": 0.0, "elevation": 0.0}, "performance_params": {"detection_range": 5000}, "work_mode": "passive_search"}]}
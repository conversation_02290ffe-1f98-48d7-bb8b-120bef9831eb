{"simulation": {"scenario_name": "性能测试场景", "duration": 60.0, "time_step": 0.1, "data_count": 1000, "output_types": ["static_images", "parameters"], "environment": {"weather_condition": "clear_weather", "temperature": 288.15, "humidity": 0.5, "pressure": 101325, "wind_speed": 3.0, "visibility": 23000}}, "system": {"max_threads": 8, "image_resolution": [640, 480], "video_fps": 30, "random_seed": 12345, "memory_limit_mb": 2048, "cpu_limit_percent": 85, "enable_performance_monitoring": true}, "optical_targets": [{"model": "性能测试_红外目标1", "position": {"latitude": 39.9042, "longitude": 116.4074, "altitude": 1000.0}, "observation_direction": {"azimuth": 0.0, "elevation": 0.0}, "performance_params": {"detection_range": 15000, "resolution": 0.1, "field_of_view": 10.0}, "work_mode": "passive_search"}, {"model": "性能测试_红外目标2", "position": {"latitude": 39.91, "longitude": 116.41, "altitude": 1200.0}, "observation_direction": {"azimuth": 90.0, "elevation": 5.0}, "performance_params": {"detection_range": 12000, "resolution": 0.12, "field_of_view": 12.0}, "work_mode": "passive_search"}, {"model": "性能测试_激光目标", "position": {"latitude": 39.9, "longitude": 116.4, "altitude": 800.0}, "observation_direction": {"azimuth": 45.0, "elevation": 10.0}, "performance_params": {"detection_range": 20000, "resolution": 0.05, "field_of_view": 8.0, "wavelength": 1.064e-06, "power": 1500}, "work_mode": "active_illumination"}], "optical_jammers": [{"model": "性能测试_烟幕干扰", "position": {"latitude": 39.905, "longitude": 116.405, "altitude": 500.0}, "jamming_direction": {"azimuth": 180.0, "elevation": 0.0}, "performance_params": {"coverage_range": 3000, "duration": 200, "coverage_radius": 100}, "work_mode": "area_denial", "jamming_strategy": "obscuration"}, {"model": "性能测试_激光干扰", "position": {"latitude": 39.895, "longitude": 116.395, "altitude": 700.0}, "jamming_direction": {"azimuth": 270.0, "elevation": 8.0}, "performance_params": {"jamming_power": 2000, "jamming_frequency": 1000, "coverage_range": 8000, "wavelength": 5.32e-07}, "work_mode": "active_dazzling", "jamming_strategy": "sensor_overload"}], "optical_recons": [{"model": "性能测试_红外侦察", "position": {"latitude": 39.92, "longitude": 116.42, "altitude": 1500.0}, "performance_params": {"detection_range": 25000, "resolution": 0.03, "spectral_coverage": [3e-06, 1.2e-05], "sensitivity": 0.9, "field_of_view": 20.0}, "work_mode": "passive_detection", "detection_mode": "infrared_warning"}, {"model": "性能测试_激光告警", "position": {"latitude": 39.915, "longitude": 116.415, "altitude": 1300.0}, "performance_params": {"detection_range": 20000, "resolution": 0.05, "spectral_coverage": [5e-07, 1.7e-06], "sensitivity": 0.95, "field_of_view": 360.0}, "work_mode": "omnidirectional_monitoring", "detection_mode": "laser_warning"}]}
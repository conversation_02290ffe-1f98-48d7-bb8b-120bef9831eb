#!/usr/bin/env python3
"""
HTTP API 测试脚本
用于测试光电对抗仿真系统的 FastAPI 接口
"""

import json
import requests
import time
from typing import Dict, Any

# API 服务器配置
API_BASE_URL = "http://localhost:8587"
API_ENDPOINT = f"{API_BASE_URL}/run_simulation"

# 测试请求数据
TEST_REQUEST_DATA = {
    "simulation": {
        "scenario_name": "系统测试场景",
        "duration": 30.0,
        "time_step": 0.1,
        "data_count": 10,
        "output_types": ["parameters"],
        "environment": {
            "weather_condition": "clear_weather",
            "temperature": 288.15,
            "humidity": 0.6,
            "pressure": 101325,
            "wind_speed": 5.0,
            "visibility": 23000
        }
    },
    "system": {
        "max_threads": 2,
        "image_resolution": [640, 480],
        "video_fps": 30,
        "random_seed": 12345
    },
    "optical_targets": [
        {
            "model": "测试红外目标",
            "position": {
                "latitude": 39.9042,
                "longitude": 116.4074,
                "altitude": 1000.0
            },
            "observation_direction": {
                "azimuth": 45.0,
                "elevation": 10.0
            },
            "performance_params": {
                "detection_range": 10000,
                "resolution": 0.1,
                "field_of_view": 10.0
            },
            "work_mode": "passive_search"
        }
    ],
    "optical_jammers": [
        {
            "model": "测试烟幕干扰",
            "position": {
                "latitude": 39.9000,
                "longitude": 116.4000,
                "altitude": 500.0
            },
            "jamming_direction": {
                "azimuth": 90.0,
                "elevation": 0.0
            },
            "performance_params": {
                "jamming_power": 500,
                "coverage_range": 2000
            },
            "work_mode": "continuous",
            "jamming_strategy": "area_denial"
        }
    ],
    "optical_recons": [
        {
            "model": "测试红外侦察",
            "position": {
                "latitude": 39.9200,
                "longitude": 116.4200,
                "altitude": 1500.0
            },
            "performance_params": {
                "detection_range": 15000,
                "resolution": 0.05,
                "spectral_coverage": [3e-6, 12e-6]
            },
            "work_mode": "passive_detection",
            "detection_mode": "infrared_warning"
        }
    ]
}

def check_api_health() -> bool:
    """检查API服务是否正常运行"""
    try:
        response = requests.get(f"{API_BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✓ API 服务健康检查通过")
            return True
        else:
            print(f"✗ API 服务健康检查失败，状态码: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ 无法连接到API服务: {e}")
        return False

def send_simulation_request(data: Dict[str, Any]) -> None:
    """发送仿真请求"""
    try:
        print("="*60)
        print("发送仿真请求...")
        print("="*60)
        
        # 显示请求数据摘要
        print(f"场景名称: {data['simulation']['scenario_name']}")
        print(f"仿真时长: {data['simulation']['duration']} 秒")
        print(f"数据数量: {data['simulation']['data_count']}")
        print(f"输出类型: {data['simulation']['output_types']}")
        print(f"目标数量: {len(data['optical_targets'])}")
        print(f"干扰器数量: {len(data['optical_jammers'])}")
        print(f"侦察设备数量: {len(data['optical_recons'])}")
        print()
        
        # 记录开始时间
        start_time = time.time()
        
        # 发送POST请求
        headers = {"Content-Type": "application/json"}
        response = requests.post(
            API_ENDPOINT,
            json=data,
            headers=headers,
            timeout=300  # 5分钟超时
        )
        
        # 记录结束时间
        end_time = time.time()
        request_duration = end_time - start_time
        
        print(f"请求耗时: {request_duration:.2f} 秒")
        print(f"响应状态码: {response.status_code}")
        print()
        
        if response.status_code == 200:
            # 解析响应
            result = response.json()
            # 保存json文件
            with open('test_result.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            print("✓ 仿真请求成功!")
            print("="*60)
            print("完整返回JSON:")
            print("="*60)
            print(json.dumps(result, ensure_ascii=False, indent=2))
            print("="*60)
            print("仿真结果摘要:")
            print("="*60)
            
            # 显示基本信息
            print(f"执行状态: {'成功' if result['success'] else '失败'}")
            
            if result['success']:
                # 会话信息
                session_info = result['session_info']
                print(f"会话ID: {session_info['session_id']}")
                print(f"执行时长: {session_info['duration']:.2f} 秒")
                print(f"输出目录: {session_info['output_directory']}")
                
                # 仿真结果统计
                simulation_results = result.get('simulation_results', {})
                total_files = 0
                for category in ['images', 'videos', 'data', 'summary']:
                    if category in simulation_results and isinstance(simulation_results[category], list):
                        file_count = len(simulation_results[category])
                        total_files += file_count
                        if file_count > 0:
                            print(f"  {category}: {file_count} 个文件")

                print(f"生成文件总数: {total_files}")
                
                # 性能指标
                performance = result['performance_metrics']
                print(f"处理速度: {performance['processing_speed']:.2f} 数据/秒")
                if 'files_per_second' in performance:
                    print(f"文件生成速度: {performance['files_per_second']:.2f} 文件/秒")
                if 'thread_count' in performance:
                    print(f"使用线程数: {performance['thread_count']}")
                if 'thread_utilization' in performance:
                    print(f"线程利用率: {performance['thread_utilization']:.2f}")
                if 'execution_efficiency' in performance:
                    efficiency = performance['execution_efficiency']
                    print(f"执行效率: {efficiency['data_per_second']:.2f} 数据/秒")
                    print(f"平均处理时间: {efficiency['time_per_data']:.4f} 秒/数据")
                
            else:
                print(f"错误信息: {result.get('error_info', '未知错误')}")
                
        else:
            print(f"✗ 请求失败，状态码: {response.status_code}")
            try:
                error_detail = response.json()
                print(f"错误详情: {error_detail}")
            except:
                print(f"响应内容: {response.text}")
                
    except requests.exceptions.Timeout:
        print("✗ 请求超时")
    except requests.exceptions.RequestException as e:
        print(f"✗ 请求异常: {e}")
    except json.JSONDecodeError as e:
        print(f"✗ JSON解析错误: {e}")
    except Exception as e:
        print(f"✗ 未知错误: {e}")

def main():
    """主函数"""
    print("光电对抗仿真系统 HTTP API 测试脚本")
    print("="*60)
    
    # 检查API服务状态
    if not check_api_health():
        print("\n请确保API服务已启动:")
        print("python http_api.py")
        return
    
    print()
    
    # 发送测试请求
    send_simulation_request(TEST_REQUEST_DATA)
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()

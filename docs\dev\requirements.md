# 功能指标

## 光电目标数据产生

通过数据接口输入场景信息，其中包括光电目标设备型号、位置、性能参数、工作模式。经系统仿真产生光电目标数据，包含参数级数据集，状态参数，图片、视频。

1. 支持自定义光电设备。
2. 允许用户设定光电设备的位置和观察方向。
3. 允许光电设备性能参数输入。
4. 支持设定光电设备的工作模式设置。
5. 输出光电目标数据集。

## 光电干扰设备数据产生

通过数据接口输入场景信息，其中包括光电干扰设备型号、位置、性能参数、工作模式。经系统仿真产生光电干扰设备数据集。

1. 支持自定义光电干扰设备型号。
2. 允许用户设定干扰设备的位置和干扰方向。
3. 提供性能参数输入。
4. 支持设定干扰设备的工作样式。
5. 输出光电干扰设备数据集，包含参数级数据集。

## 光电对抗侦察设备数据产生

通过数据接口输入场景信息，其中包括光电对抗侦察设备型号、位置、初始工作参数、工作模式、光电目标位置、型号、状态参数。经系统仿真产生光电对抗侦察设备数据，输出参数级数据集。

1. 支持自定义光电侦察设备模型。
2. 允许设定侦察设备的位置和初始工作参数。
3. 支持性能参数输入。
4. 支持设定侦察设备的工作模式。
5. 能够输出参数级数据集，包含侦察到的光电信号参数。

## 单站点分选识别

模拟光电对抗侦察设备的信号分选和识别过程，包括图像识别、特征提取、目标跟踪等

# 性能指标

## 光电目标数据产生

1. 自定义光电设备 ≥ 3 类，至少包括红外、激光、电视等光电传感器。
2. 输入光电设备性能参数 ≥ 3 类，至少包括探测距离、分辨率、视场角。
3. 设置光电设备的工作模式 ≥ 2 类，至少包括被动搜索、主动照射。
4. 输出光电目标数据类型 ≥ 2 类，至少包括状态参数、捕获图片/视频。

## 光电干扰设备数据产生

1. 自定义光电干扰设备类型 ≥ 2 类，至少包括烟幕、红外诱饵弹。
2. 输入性能参数 ≥ 3 类，至少包括干扰功率、干扰频段、干扰策略。

## 光电对抗侦察设备数据产生

1. 自定义光电侦察设备模型 ≥ 2 类，至少包括红外侦测系统、光电信号分析器。
2. 输入性能参数 ≥ 3 类，至少包括探测距离、分辨率、光谱覆盖范围。
3. 设置侦察设备工作模式 ≥ 2 类，至少包括激光告警、远红外告警。

## 单站点分选识别

完整模拟光电对抗侦察设备的信号分选和识别过程，包括图像识别、特征提取、目标跟踪等
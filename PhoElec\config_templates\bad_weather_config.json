{"simulation": {"scenario_name": "恶劣天气光电对抗场景", "duration": 150.0, "time_step": 0.2, "data_count": 400, "output_types": ["static_images", "parameters"], "environment": {"weather_condition": "fog", "temperature": 278.15, "humidity": 0.95, "pressure": 100800, "wind_speed": 12.0, "visibility": 500, "cloud_cover": 1.0, "precipitation": 5.0, "atmospheric_turbulence": 0.8, "fog_density": 0.1, "rain_rate": 10.0}}, "system": {"max_threads": 4, "image_resolution": [640, 480], "video_fps": 15, "random_seed": 8901}, "optical_targets": [{"model": "恶劣天气红外目标_增强", "position": {"latitude": 39.9042, "longitude": 116.4074, "altitude": 800.0}, "observation_direction": {"azimuth": 0.0, "elevation": 5.0}, "performance_params": {"detection_range": 3000, "resolution": 0.2, "field_of_view": 25.0, "spectral_range": [8e-06, 1.2e-05], "sensitivity": 0.95, "weather_penetration": 0.3}, "work_mode": "weather_enhanced_search", "temperature": {"engine": 450.0, "body": 285.0, "exhaust": 600.0, "background": 278.15}, "weather_adaptation": {"fog_compensation": true, "rain_filtering": true, "contrast_enhancement": 2.0}}], "optical_jammers": [{"model": "恶劣天气烟幕_持久型", "position": {"latitude": 39.9, "longitude": 116.4, "altitude": 200.0}, "jamming_direction": {"azimuth": 0.0, "elevation": 0.0}, "performance_params": {"coverage_range": 2000, "duration": 900, "coverage_radius": 300, "weather_persistence": 0.8, "wind_resistance": 0.6}, "work_mode": "persistent_obscuration", "jamming_strategy": "weather_enhanced_screening", "weather_interaction": {"rain_washout_rate": 0.1, "wind_dispersion_factor": 2.0, "humidity_absorption": 0.3}}], "optical_recons": [{"model": "恶劣天气侦察_穿透型", "position": {"latitude": 39.92, "longitude": 116.42, "altitude": 1500.0}, "performance_params": {"detection_range": 5000, "resolution": 0.1, "spectral_coverage": [8e-06, 1.4e-05], "sensitivity": 0.99, "field_of_view": 30.0, "weather_penetration_capability": 0.7}, "work_mode": "weather_penetrating_surveillance", "detection_mode": "thermal_infrared_enhanced", "weather_compensation": {"atmospheric_correction": true, "scattering_compensation": true, "absorption_correction": true, "turbulence_mitigation": true}}]}
"""
数据格式化工具模块
提供各种数据格式的输出和转换功能
"""

import json
import csv
import xml.etree.ElementTree as ET
import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from datetime import datetime
import h5py

from .logger import LoggerMixin


logger = logging.getLogger(__name__)


class DataFormatter(LoggerMixin):
    """数据格式化器"""
    
    def __init__(self):
        """初始化数据格式化器"""
        self.supported_formats = ['json', 'csv', 'xml', 'excel', 'hdf5', 'txt']
        self.logger.info("数据格式化器初始化完成")
    
    def format_simulation_results(
        self,
        results: Dict[str, Any],
        output_format: str = 'json'
    ) -> Union[str, bytes]:
        """
        格式化仿真结果
        
        Args:
            results: 仿真结果数据
            output_format: 输出格式
            
        Returns:
            格式化后的数据
        """
        if output_format not in self.supported_formats:
            raise ValueError(f"不支持的格式: {output_format}")
        
        if output_format == 'json':
            return self._to_json(results)
        elif output_format == 'xml':
            return self._to_xml(results)
        elif output_format == 'txt':
            return self._to_text(results)
        else:
            raise ValueError(f"格式 {output_format} 需要文件路径")
    
    def save_formatted_data(
        self,
        data: Any,
        file_path: str,
        format_type: Optional[str] = None
    ) -> str:
        """
        保存格式化数据到文件
        
        Args:
            data: 要保存的数据
            file_path: 文件路径
            format_type: 格式类型，None表示从文件扩展名推断
            
        Returns:
            保存的文件路径
        """
        path = Path(file_path)
        
        if format_type is None:
            format_type = path.suffix.lower().lstrip('.')
        
        # 确保目录存在
        path.parent.mkdir(parents=True, exist_ok=True)
        
        if format_type == 'json':
            self._save_json(data, file_path)
        elif format_type == 'csv':
            self._save_csv(data, file_path)
        elif format_type == 'xml':
            self._save_xml(data, file_path)
        elif format_type in ['xlsx', 'excel']:
            self._save_excel(data, file_path)
        elif format_type in ['h5', 'hdf5']:
            self._save_hdf5(data, file_path)
        elif format_type == 'txt':
            self._save_text(data, file_path)
        else:
            raise ValueError(f"不支持的格式: {format_type}")
        
        self.logger.debug(f"数据已保存到: {file_path}")
        return file_path
    
    def _to_json(self, data: Any) -> str:
        """转换为JSON格式"""
        def json_serializer(obj):
            """JSON序列化器"""
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, datetime):
                return obj.isoformat()
            elif hasattr(obj, '__dict__'):
                return obj.__dict__
            else:
                return str(obj)
        
        return json.dumps(data, ensure_ascii=False, indent=2, default=json_serializer)
    
    def _to_xml(self, data: Any, root_name: str = 'simulation_data') -> str:
        """转换为XML格式"""
        root = ET.Element(root_name)
        self._dict_to_xml(data, root)
        
        # 格式化XML
        self._indent_xml(root)
        return ET.tostring(root, encoding='unicode')
    
    def _dict_to_xml(self, data: Any, parent: ET.Element):
        """递归转换字典到XML"""
        if isinstance(data, dict):
            for key, value in data.items():
                # 处理特殊字符
                safe_key = str(key).replace(' ', '_').replace('-', '_')
                child = ET.SubElement(parent, safe_key)
                self._dict_to_xml(value, child)
        elif isinstance(data, list):
            for i, item in enumerate(data):
                child = ET.SubElement(parent, f'item_{i}')
                self._dict_to_xml(item, child)
        else:
            parent.text = str(data)
    
    def _indent_xml(self, elem: ET.Element, level: int = 0):
        """格式化XML缩进"""
        i = "\n" + level * "  "
        if len(elem):
            if not elem.text or not elem.text.strip():
                elem.text = i + "  "
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
            for elem in elem:
                self._indent_xml(elem, level + 1)
            if not elem.tail or not elem.tail.strip():
                elem.tail = i
        else:
            if level and (not elem.tail or not elem.tail.strip()):
                elem.tail = i
    
    def _to_text(self, data: Any) -> str:
        """转换为文本格式"""
        lines = []
        lines.append("=" * 60)
        lines.append("光电对抗仿真系统 - 数据报告")
        lines.append("=" * 60)
        lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")
        
        self._dict_to_text(data, lines, 0)
        
        return "\n".join(lines)
    
    def _dict_to_text(self, data: Any, lines: List[str], indent: int):
        """递归转换字典到文本"""
        prefix = "  " * indent
        
        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, (dict, list)):
                    lines.append(f"{prefix}{key}:")
                    self._dict_to_text(value, lines, indent + 1)
                else:
                    lines.append(f"{prefix}{key}: {value}")
        elif isinstance(data, list):
            for i, item in enumerate(data):
                lines.append(f"{prefix}[{i}]:")
                self._dict_to_text(item, lines, indent + 1)
        else:
            lines.append(f"{prefix}{data}")
    
    def _save_json(self, data: Any, file_path: str):
        """保存JSON文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json_str = self._to_json(data)
            f.write(json_str)
    
    def _save_csv(self, data: Any, file_path: str):
        """保存CSV文件"""
        if isinstance(data, list) and data and isinstance(data[0], dict):
            # 列表字典格式
            df = pd.DataFrame(data)
            df.to_csv(file_path, index=False, encoding='utf-8-sig')
        elif isinstance(data, dict):
            # 字典格式，尝试转换为DataFrame
            try:
                df = pd.DataFrame(data)
                df.to_csv(file_path, encoding='utf-8-sig')
            except:
                # 如果无法转换，保存为键值对
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
                    writer = csv.writer(f)
                    writer.writerow(['Key', 'Value'])
                    for key, value in data.items():
                        writer.writerow([key, str(value)])
        else:
            raise ValueError("CSV格式要求数据为列表字典或字典格式")
    
    def _save_xml(self, data: Any, file_path: str):
        """保存XML文件"""
        xml_str = self._to_xml(data)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('<?xml version="1.0" encoding="UTF-8"?>\n')
            f.write(xml_str)
    
    def _save_excel(self, data: Any, file_path: str):
        """保存Excel文件"""
        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            if isinstance(data, dict):
                for sheet_name, sheet_data in data.items():
                    if isinstance(sheet_data, list) and sheet_data and isinstance(sheet_data[0], dict):
                        df = pd.DataFrame(sheet_data)
                        # 限制工作表名称长度
                        safe_sheet_name = str(sheet_name)[:31]
                        df.to_excel(writer, sheet_name=safe_sheet_name, index=False)
                    elif isinstance(sheet_data, dict):
                        df = pd.DataFrame([sheet_data])
                        safe_sheet_name = str(sheet_name)[:31]
                        df.to_excel(writer, sheet_name=safe_sheet_name, index=False)
            elif isinstance(data, list) and data and isinstance(data[0], dict):
                df = pd.DataFrame(data)
                df.to_excel(writer, sheet_name='Data', index=False)
            else:
                # 创建简单的数据表
                df = pd.DataFrame({'Data': [str(data)]})
                df.to_excel(writer, sheet_name='Data', index=False)
    
    def _save_hdf5(self, data: Any, file_path: str):
        """保存HDF5文件"""
        with h5py.File(file_path, 'w') as f:
            self._dict_to_hdf5(data, f)
    
    def _dict_to_hdf5(self, data: Any, group: h5py.Group, name: str = ''):
        """递归保存字典到HDF5"""
        if isinstance(data, dict):
            for key, value in data.items():
                safe_key = str(key).replace('/', '_')
                if isinstance(value, (dict, list)):
                    subgroup = group.create_group(safe_key)
                    self._dict_to_hdf5(value, subgroup)
                else:
                    try:
                        if isinstance(value, str):
                            group.create_dataset(safe_key, data=value.encode('utf-8'))
                        else:
                            group.create_dataset(safe_key, data=value)
                    except Exception as e:
                        # 如果无法保存，转换为字符串
                        group.create_dataset(safe_key, data=str(value).encode('utf-8'))
        elif isinstance(data, list):
            for i, item in enumerate(data):
                self._dict_to_hdf5(item, group, f'item_{i}')
        else:
            try:
                if isinstance(data, str):
                    group.create_dataset('value', data=data.encode('utf-8'))
                else:
                    group.create_dataset('value', data=data)
            except:
                group.create_dataset('value', data=str(data).encode('utf-8'))
    
    def _save_text(self, data: Any, file_path: str):
        """保存文本文件"""
        text_content = self._to_text(data)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(text_content)


class ReportGenerator(LoggerMixin):
    """报告生成器"""
    
    def __init__(self):
        """初始化报告生成器"""
        self.formatter = DataFormatter()
    
    def generate_simulation_report(
        self,
        simulation_data: Dict[str, Any],
        output_path: str,
        report_format: str = 'html'
    ) -> str:
        """
        生成仿真报告
        
        Args:
            simulation_data: 仿真数据
            output_path: 输出路径
            report_format: 报告格式 ('html', 'markdown', 'pdf')
            
        Returns:
            报告文件路径
        """
        if report_format == 'html':
            return self._generate_html_report(simulation_data, output_path)
        elif report_format == 'markdown':
            return self._generate_markdown_report(simulation_data, output_path)
        else:
            raise ValueError(f"不支持的报告格式: {report_format}")
    
    def _generate_html_report(self, data: Dict[str, Any], output_path: str) -> str:
        """生成HTML报告"""
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>光电对抗仿真报告</title>
    <style>
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }}
        .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }}
        .data-table {{ width: 100%; border-collapse: collapse; }}
        .data-table th, .data-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        .data-table th {{ background-color: #f2f2f2; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>光电对抗仿真系统报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="section">
        <h2>仿真概要</h2>
        <p>场景名称: {data.get('scenario_name', '未知')}</p>
        <p>仿真时长: {data.get('duration', 0)} 秒</p>
        <p>数据生成数量: {data.get('data_count', 0)} 条</p>
    </div>
    
    <div class="section">
        <h2>设备配置</h2>
        {self._generate_device_summary_html(data)}
    </div>
    
    <div class="section">
        <h2>仿真结果</h2>
        {self._generate_results_summary_html(data)}
    </div>
</body>
</html>
        """
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return output_path
    
    def _generate_device_summary_html(self, data: Dict[str, Any]) -> str:
        """生成设备摘要HTML"""
        device_counts = data.get('device_count', {})
        
        html = "<table class='data-table'>"
        html += "<tr><th>设备类型</th><th>数量</th></tr>"
        
        device_names = {
            'optical_targets': '光电目标设备',
            'optical_jammers': '光电干扰设备',
            'optical_recons': '光电侦察设备'
        }
        
        for device_type, count in device_counts.items():
            name = device_names.get(device_type, device_type)
            html += f"<tr><td>{name}</td><td>{count}</td></tr>"
        
        html += "</table>"
        return html
    
    def _generate_results_summary_html(self, data: Dict[str, Any]) -> str:
        """生成结果摘要HTML"""
        results = data.get('results_summary', {})
        
        html = "<table class='data-table'>"
        html += "<tr><th>输出类型</th><th>文件数量</th></tr>"
        
        for output_type, files in results.items():
            if isinstance(files, list):
                html += f"<tr><td>{output_type}</td><td>{len(files)}</td></tr>"
        
        html += "</table>"
        return html
    
    def _generate_markdown_report(self, data: Dict[str, Any], output_path: str) -> str:
        """生成Markdown报告"""
        markdown_content = f"""# 光电对抗仿真系统报告

**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 仿真概要

- **场景名称**: {data.get('scenario_name', '未知')}
- **仿真时长**: {data.get('duration', 0)} 秒
- **数据生成数量**: {data.get('data_count', 0)} 条

## 设备配置

{self._generate_device_summary_markdown(data)}

## 仿真结果

{self._generate_results_summary_markdown(data)}

---
*报告由光电对抗仿真系统自动生成*
        """
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        return output_path
    
    def _generate_device_summary_markdown(self, data: Dict[str, Any]) -> str:
        """生成设备摘要Markdown"""
        device_counts = data.get('device_count', {})
        
        markdown = "| 设备类型 | 数量 |\n|----------|------|\n"
        
        device_names = {
            'optical_targets': '光电目标设备',
            'optical_jammers': '光电干扰设备',
            'optical_recons': '光电侦察设备'
        }
        
        for device_type, count in device_counts.items():
            name = device_names.get(device_type, device_type)
            markdown += f"| {name} | {count} |\n"
        
        return markdown
    
    def _generate_results_summary_markdown(self, data: Dict[str, Any]) -> str:
        """生成结果摘要Markdown"""
        results = data.get('results_summary', {})
        
        markdown = "| 输出类型 | 文件数量 |\n|----------|----------|\n"
        
        for output_type, files in results.items():
            if isinstance(files, list):
                markdown += f"| {output_type} | {len(files)} |\n"
        
        return markdown

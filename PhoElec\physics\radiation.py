"""
辐射物理模型
实现红外辐射、激光辐射等相关的物理计算
"""

import numpy as np
import logging
from typing import Dict, Tuple, Union, Optional

from .constants import (
    STEFAN_BOLTZMANN_CONSTANT, PLANCK_CONSTANT, SPEED_OF_LIGHT,
    BOLTZMANN_CONSTANT, WAVELENGTH_RANGES, MATERIAL_EMISSIVITY
)
from ..utils.logger import LoggerMixin


logger = logging.getLogger(__name__)


class BlackbodyRadiation(LoggerMixin):
    """黑体辐射模型"""
    
    @staticmethod
    def planck_function(wavelength: float, temperature: float) -> float:
        """
        普朗克函数 - 计算黑体在特定波长和温度下的光谱辐射亮度
        
        Args:
            wavelength: 波长 (m)
            temperature: 温度 (K)
            
        Returns:
            光谱辐射亮度 (W⋅m⁻²⋅sr⁻¹⋅m⁻¹)
        """
        if temperature <= 0:
            return 0.0
        
        if wavelength <= 0:
            return 0.0
        
        try:
            # 普朗克函数: B(λ,T) = (2hc²/λ⁵) * 1/(exp(hc/λkT) - 1)
            c1 = 2 * PLANCK_CONSTANT * SPEED_OF_LIGHT**2
            c2 = PLANCK_CONSTANT * SPEED_OF_LIGHT / (BOLTZMANN_CONSTANT * temperature)
            
            numerator = c1 / (wavelength**5)
            denominator = np.exp(c2 / wavelength) - 1
            
            return numerator / denominator
            
        except (OverflowError, ZeroDivisionError):
            return 0.0
    
    @staticmethod
    def stefan_boltzmann_law(temperature: float, emissivity: float = 1.0) -> float:
        """
        斯蒂芬-玻尔兹曼定律 - 计算总辐射功率
        
        Args:
            temperature: 温度 (K)
            emissivity: 发射率 (0-1)
            
        Returns:
            单位面积辐射功率 (W/m²)
        """
        if temperature <= 0:
            return 0.0
        
        return emissivity * STEFAN_BOLTZMANN_CONSTANT * (temperature**4)
    
    @staticmethod
    def wien_displacement_law(temperature: float) -> float:
        """
        维恩位移定律 - 计算峰值波长
        
        Args:
            temperature: 温度 (K)
            
        Returns:
            峰值波长 (m)
        """
        if temperature <= 0:
            return float('inf')
        
        wien_constant = 2.897771955e-3  # m⋅K
        return wien_constant / temperature
    
    @classmethod
    def spectral_radiance_band(
        cls,
        temperature: float,
        wavelength_range: Tuple[float, float],
        num_points: int = 100
    ) -> float:
        """
        计算特定波段内的积分辐射亮度
        
        Args:
            temperature: 温度 (K)
            wavelength_range: 波长范围 (m)
            num_points: 积分点数
            
        Returns:
            波段积分辐射亮度 (W⋅m⁻²⋅sr⁻¹)
        """
        wavelengths = np.linspace(wavelength_range[0], wavelength_range[1], num_points)
        radiances = [cls.planck_function(wl, temperature) for wl in wavelengths]
        
        # 使用梯形积分
        return np.trapz(radiances, wavelengths)


class TargetRadiation(LoggerMixin):
    """目标辐射特性模型"""
    
    def __init__(self, target_config: Dict):
        """
        初始化目标辐射模型
        
        Args:
            target_config: 目标配置参数
        """
        self.config = target_config
        self.temperature_map = self._initialize_temperature_map()
        self.emissivity_map = self._initialize_emissivity_map()
    
    def _initialize_temperature_map(self) -> Dict[str, float]:
        """初始化温度分布图"""
        # 从配置中获取温度信息，或使用默认值
        temp_config = self.config.get('temperature', {})
        
        default_temps = {
            'engine': temp_config.get('engine', 400.0),
            'body': temp_config.get('body', 300.0),
            'exhaust': temp_config.get('exhaust', 600.0),
            'background': temp_config.get('background', 280.0)
        }
        
        return default_temps
    
    def _initialize_emissivity_map(self) -> Dict[str, float]:
        """初始化发射率分布图"""
        # 从配置中获取发射率信息，或使用默认值
        emiss_config = self.config.get('emissivity', {})
        
        default_emiss = {
            'engine': emiss_config.get('engine', 0.8),
            'body': emiss_config.get('body', 0.9),
            'exhaust': emiss_config.get('exhaust', 0.95),
            'background': emiss_config.get('background', 0.9)
        }
        
        return default_emiss
    
    def get_radiant_intensity(
        self,
        component: str,
        wavelength_range: Optional[Tuple[float, float]] = None,
        area: float = 1.0
    ) -> float:
        """
        计算目标组件的辐射强度
        
        Args:
            component: 目标组件名称
            wavelength_range: 波长范围，None表示全波段
            area: 辐射面积 (m²)
            
        Returns:
            辐射强度 (W/sr)
        """
        temperature = self.temperature_map.get(component, 300.0)
        emissivity = self.emissivity_map.get(component, 0.9)
        
        if wavelength_range is None:
            # 全波段辐射
            radiant_exitance = BlackbodyRadiation.stefan_boltzmann_law(temperature, emissivity)
            # 假设朗伯辐射体，立体角为π
            return radiant_exitance * area / np.pi
        else:
            # 特定波段辐射
            spectral_radiance = BlackbodyRadiation.spectral_radiance_band(
                temperature, wavelength_range
            )
            return spectral_radiance * emissivity * area
    
    def get_total_radiant_intensity(
        self,
        wavelength_range: Optional[Tuple[float, float]] = None
    ) -> float:
        """
        计算目标总辐射强度
        
        Args:
            wavelength_range: 波长范围
            
        Returns:
            总辐射强度 (W/sr)
        """
        total_intensity = 0.0
        
        # 获取各组件面积配置
        areas = self.config.get('component_areas', {
            'engine': 0.5,
            'body': 10.0,
            'exhaust': 0.2,
        })
        
        for component, area in areas.items():
            intensity = self.get_radiant_intensity(component, wavelength_range, area)
            total_intensity += intensity
        
        return total_intensity
    
    def apply_temperature_variation(self, time: float):
        """
        应用温度随时间的变化
        
        Args:
            time: 时间 (s)
        """
        # 简单的周期性温度变化模型
        variation_amplitude = self.config.get('temperature_variation', 10.0)
        variation_period = self.config.get('variation_period', 60.0)
        
        variation = variation_amplitude * np.sin(2 * np.pi * time / variation_period)
        
        for component in self.temperature_map:
            base_temp = self.config.get('temperature', {}).get(component, 300.0)
            self.temperature_map[component] = base_temp + variation


class LaserRadiation(LoggerMixin):
    """激光辐射模型"""
    
    def __init__(self, laser_config: Dict):
        """
        初始化激光辐射模型
        
        Args:
            laser_config: 激光配置参数
        """
        self.config = laser_config
        self.wavelength = laser_config.get('wavelength', 1.064e-6)
        self.power = laser_config.get('power', 1000.0)
        self.beam_divergence = laser_config.get('beam_divergence', 1e-3)
        self.pulse_duration = laser_config.get('pulse_duration', 10e-9)
        self.repetition_rate = laser_config.get('repetition_rate', 10)
    
    def gaussian_beam_intensity(
        self,
        distance: float,
        radial_offset: float = 0.0,
        beam_waist: Optional[float] = None
    ) -> float:
        """
        计算高斯光束强度分布
        
        Args:
            distance: 传播距离 (m)
            radial_offset: 径向偏移 (m)
            beam_waist: 光束腰斑半径 (m)
            
        Returns:
            光强 (W/m²)
        """
        if beam_waist is None:
            # 根据发散角估算腰斑半径
            beam_waist = self.beam_divergence * distance / 2
        
        # 高斯光束传播
        rayleigh_range = np.pi * beam_waist**2 / self.wavelength
        beam_radius = beam_waist * np.sqrt(1 + (distance / rayleigh_range)**2)
        
        # 高斯强度分布
        peak_intensity = 2 * self.power / (np.pi * beam_radius**2)
        intensity = peak_intensity * np.exp(-2 * (radial_offset / beam_radius)**2)
        
        return intensity
    
    def pulse_energy(self) -> float:
        """
        计算单脉冲能量
        
        Returns:
            脉冲能量 (J)
        """
        if self.repetition_rate > 0:
            return self.power / self.repetition_rate
        else:
            return self.power * self.pulse_duration
    
    def average_power_density(self, distance: float, area: float) -> float:
        """
        计算平均功率密度
        
        Args:
            distance: 距离 (m)
            area: 照射面积 (m²)
            
        Returns:
            平均功率密度 (W/m²)
        """
        beam_area = np.pi * (self.beam_divergence * distance)**2
        effective_area = min(beam_area, area)
        
        return self.power / effective_area if effective_area > 0 else 0.0

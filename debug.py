# 使用PhoElec\config_templates\multi_target_config.json进行http请求测试，并保存json

import requests
import json

url = "http://localhost:8587/run_simulation"

with open('PhoElec\config_templates\multi_target_config.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

    response = requests.post(url, json=data)
    if response.status_code == 200:
        result = response.json()
        with open('result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
    else:
        print(f"HTTP请求失败: {response.status_code}")
        print(response.text)
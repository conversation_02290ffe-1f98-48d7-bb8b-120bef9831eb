"""
性能优化工具模块
提供性能监控、内存管理、缓存等优化功能
"""

import time
import psutil
import threading
import gc
import logging
from typing import Dict, Any, Optional, Callable, List
from functools import wraps, lru_cache
from collections import defaultdict, deque
import numpy as np

from .logger import LoggerMixin


logger = logging.getLogger(__name__)


class PerformanceMonitor(LoggerMixin):
    """性能监控器"""
    
    def __init__(self, monitor_interval: float = 1.0):
        """
        初始化性能监控器
        
        Args:
            monitor_interval: 监控间隔（秒）
        """
        self.monitor_interval = monitor_interval
        self.monitoring = False
        self.monitor_thread = None
        self.metrics_history = defaultdict(deque)
        self.max_history_size = 1000
        self._lock = threading.Lock()
        
        # 获取进程对象
        self.process = psutil.Process()
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        self.logger.info("性能监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5.0)
        self.logger.info("性能监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                metrics = self._collect_metrics()
                self._record_metrics(metrics)
                time.sleep(self.monitor_interval)
            except Exception as e:
                self.logger.error(f"性能监控异常: {e}")
    
    def _collect_metrics(self) -> Dict[str, Any]:
        """收集性能指标"""
        # CPU使用率
        cpu_percent = self.process.cpu_percent()
        
        # 内存使用情况
        memory_info = self.process.memory_info()
        memory_percent = self.process.memory_percent()
        
        # 系统整体情况
        system_cpu = psutil.cpu_percent()
        system_memory = psutil.virtual_memory()
        
        # 线程数
        num_threads = self.process.num_threads()
        
        # 文件描述符数（Unix系统）
        try:
            num_fds = self.process.num_fds()
        except AttributeError:
            num_fds = 0
        
        return {
            'timestamp': time.time(),
            'cpu_percent': cpu_percent,
            'memory_rss_mb': memory_info.rss / 1024 / 1024,
            'memory_vms_mb': memory_info.vms / 1024 / 1024,
            'memory_percent': memory_percent,
            'system_cpu_percent': system_cpu,
            'system_memory_percent': system_memory.percent,
            'num_threads': num_threads,
            'num_fds': num_fds
        }
    
    def _record_metrics(self, metrics: Dict[str, Any]):
        """记录指标"""
        with self._lock:
            for key, value in metrics.items():
                history = self.metrics_history[key]
                history.append(value)
                
                # 限制历史记录大小
                if len(history) > self.max_history_size:
                    history.popleft()
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """获取当前指标"""
        return self._collect_metrics()
    
    def get_metrics_summary(self, duration_seconds: Optional[float] = None) -> Dict[str, Any]:
        """
        获取指标摘要
        
        Args:
            duration_seconds: 统计时间段（秒），None表示全部历史
            
        Returns:
            指标摘要
        """
        with self._lock:
            summary = {}
            current_time = time.time()
            
            for metric_name, history in self.metrics_history.items():
                if not history:
                    continue
                
                # 过滤时间范围
                if duration_seconds is not None and metric_name == 'timestamp':
                    cutoff_time = current_time - duration_seconds
                    filtered_values = [v for v in history if v >= cutoff_time]
                else:
                    filtered_values = list(history)
                
                if not filtered_values:
                    continue
                
                if metric_name != 'timestamp':
                    summary[metric_name] = {
                        'current': filtered_values[-1] if filtered_values else 0,
                        'min': min(filtered_values),
                        'max': max(filtered_values),
                        'avg': sum(filtered_values) / len(filtered_values),
                        'count': len(filtered_values)
                    }
            
            return summary
    
    def check_resource_limits(self) -> Dict[str, bool]:
        """检查资源限制"""
        metrics = self.get_current_metrics()
        
        warnings = {}
        
        # CPU使用率检查
        if metrics['cpu_percent'] > 90:
            warnings['high_cpu'] = True
        
        # 内存使用率检查
        if metrics['memory_percent'] > 85:
            warnings['high_memory'] = True
        
        # 系统资源检查
        if metrics['system_cpu_percent'] > 95:
            warnings['system_high_cpu'] = True
        
        if metrics['system_memory_percent'] > 90:
            warnings['system_high_memory'] = True
        
        # 线程数检查
        if metrics['num_threads'] > 100:
            warnings['too_many_threads'] = True
        
        return warnings


class MemoryManager(LoggerMixin):
    """内存管理器"""
    
    def __init__(self, gc_threshold_mb: float = 500):
        """
        初始化内存管理器
        
        Args:
            gc_threshold_mb: 垃圾回收阈值（MB）
        """
        self.gc_threshold_mb = gc_threshold_mb
        self.last_gc_time = time.time()
        self.gc_interval = 30  # 最小垃圾回收间隔（秒）
    
    def check_memory_usage(self) -> Dict[str, float]:
        """检查内存使用情况"""
        process = psutil.Process()
        memory_info = process.memory_info()
        
        return {
            'rss_mb': memory_info.rss / 1024 / 1024,
            'vms_mb': memory_info.vms / 1024 / 1024,
            'percent': process.memory_percent()
        }
    
    def force_garbage_collection(self):
        """强制垃圾回收"""
        start_time = time.time()
        
        # 执行垃圾回收
        collected = gc.collect()
        
        gc_time = time.time() - start_time
        self.last_gc_time = time.time()
        
        self.logger.debug(f"垃圾回收完成，回收对象数: {collected}，耗时: {gc_time:.3f}秒")
        
        return {
            'collected_objects': collected,
            'gc_time': gc_time
        }
    
    def auto_garbage_collection(self):
        """自动垃圾回收"""
        current_time = time.time()
        
        # 检查是否需要垃圾回收
        if current_time - self.last_gc_time < self.gc_interval:
            return None
        
        memory_usage = self.check_memory_usage()
        
        if memory_usage['rss_mb'] > self.gc_threshold_mb:
            return self.force_garbage_collection()
        
        return None
    
    def optimize_numpy_memory(self, arrays: List[np.ndarray]) -> List[np.ndarray]:
        """优化NumPy数组内存使用"""
        optimized_arrays = []
        
        for arr in arrays:
            # 检查是否可以使用更小的数据类型
            if arr.dtype == np.float64:
                # 尝试转换为float32
                if np.allclose(arr, arr.astype(np.float32)):
                    arr = arr.astype(np.float32)
            elif arr.dtype == np.int64:
                # 尝试转换为更小的整数类型
                if arr.min() >= np.iinfo(np.int32).min and arr.max() <= np.iinfo(np.int32).max:
                    arr = arr.astype(np.int32)
                elif arr.min() >= np.iinfo(np.int16).min and arr.max() <= np.iinfo(np.int16).max:
                    arr = arr.astype(np.int16)
            
            # 确保数组是连续的
            if not arr.flags['C_CONTIGUOUS']:
                arr = np.ascontiguousarray(arr)
            
            optimized_arrays.append(arr)
        
        return optimized_arrays


class CacheManager(LoggerMixin):
    """缓存管理器"""
    
    def __init__(self, max_cache_size: int = 1000):
        """
        初始化缓存管理器
        
        Args:
            max_cache_size: 最大缓存大小
        """
        self.max_cache_size = max_cache_size
        self.cache_stats = defaultdict(int)
        self._lock = threading.Lock()
    
    def cached_function(self, maxsize: int = 128):
        """
        缓存装饰器
        
        Args:
            maxsize: 最大缓存条目数
        """
        def decorator(func):
            cached_func = lru_cache(maxsize=maxsize)(func)
            
            @wraps(func)
            def wrapper(*args, **kwargs):
                with self._lock:
                    self.cache_stats[func.__name__ + '_calls'] += 1
                
                try:
                    result = cached_func(*args, **kwargs)
                    
                    with self._lock:
                        self.cache_stats[func.__name__ + '_hits'] += 1
                    
                    return result
                    
                except Exception as e:
                    with self._lock:
                        self.cache_stats[func.__name__ + '_misses'] += 1
                    raise
            
            wrapper.cache_info = cached_func.cache_info
            wrapper.cache_clear = cached_func.cache_clear
            
            return wrapper
        
        return decorator
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            return dict(self.cache_stats)
    
    def clear_all_caches(self):
        """清除所有缓存"""
        # 这里可以添加清除特定缓存的逻辑
        with self._lock:
            self.cache_stats.clear()
        
        self.logger.info("所有缓存已清除")


def performance_profile(func: Callable) -> Callable:
    """
    性能分析装饰器
    
    Args:
        func: 被装饰的函数
        
    Returns:
        装饰后的函数
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        # 记录开始时间和内存
        start_time = time.time()
        process = psutil.Process()
        start_memory = process.memory_info().rss
        
        try:
            result = func(*args, **kwargs)
            
            # 记录结束时间和内存
            end_time = time.time()
            end_memory = process.memory_info().rss
            
            # 计算性能指标
            execution_time = end_time - start_time
            memory_delta = (end_memory - start_memory) / 1024 / 1024  # MB
            
            logger.debug(
                f"函数 {func.__name__} 性能分析: "
                f"执行时间={execution_time:.3f}s, "
                f"内存变化={memory_delta:+.2f}MB"
            )
            
            return result
            
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.error(
                f"函数 {func.__name__} 执行失败: {e}, "
                f"执行时间={execution_time:.3f}s"
            )
            raise
    
    return wrapper


class ResourceLimiter(LoggerMixin):
    """资源限制器"""
    
    def __init__(self, max_memory_mb: float = 2048, max_cpu_percent: float = 80):
        """
        初始化资源限制器
        
        Args:
            max_memory_mb: 最大内存使用量（MB）
            max_cpu_percent: 最大CPU使用率（%）
        """
        self.max_memory_mb = max_memory_mb
        self.max_cpu_percent = max_cpu_percent
        self.process = psutil.Process()
    
    def check_limits(self) -> Dict[str, bool]:
        """检查资源限制"""
        violations = {}
        
        # 检查内存限制
        memory_mb = self.process.memory_info().rss / 1024 / 1024
        if memory_mb > self.max_memory_mb:
            violations['memory_exceeded'] = True
            self.logger.warning(f"内存使用超限: {memory_mb:.1f}MB > {self.max_memory_mb}MB")
        
        # 检查CPU限制
        cpu_percent = self.process.cpu_percent()
        if cpu_percent > self.max_cpu_percent:
            violations['cpu_exceeded'] = True
            self.logger.warning(f"CPU使用超限: {cpu_percent:.1f}% > {self.max_cpu_percent}%")
        
        return violations
    
    def enforce_limits(self):
        """强制执行资源限制"""
        violations = self.check_limits()
        
        if violations.get('memory_exceeded'):
            # 强制垃圾回收
            gc.collect()
            self.logger.info("由于内存超限，已执行垃圾回收")
        
        if violations.get('cpu_exceeded'):
            # 可以在这里添加CPU限制逻辑，比如降低线程优先级
            self.logger.info("CPU使用率过高，建议减少并发任务")
        
        return violations

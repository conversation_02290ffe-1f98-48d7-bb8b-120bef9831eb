# 配置模板索引

## 📁 配置模板总览

本文件夹包含了 **18个** 配置模板，涵盖了光电对抗仿真系统的各种使用场景。

## 🚀 快速选择指南

### 我是新用户，想快速体验系统
```bash
# 最快速的测试
python run_simulation.py PhoElec/config_templates/quick_test_config.json

# 基础功能体验
python run_simulation.py PhoElec/config_templates/basic_config.json
```

### 我想测试特定类型的设备
| 设备类型 | 配置文件 | 说明 |
|---------|---------|------|
| 红外目标 | `infrared_target_config.json` | 红外成像、温度分布 |
| 激光目标 | `laser_target_config.json` | 激光测距、指示 |
| 电视目标 | `tv_target_config.json` | 可见光成像 |
| 干扰设备 | `jammer_config.json` | 烟幕、激光、诱饵 |
| 侦察设备 | `recon_config.json` | 预警、分析、跟踪 |

### 我想测试特定环境条件
| 环境条件 | 配置文件 | 特点 |
|---------|---------|------|
| 晴朗天气 | `clear_weather_config.json` | 最佳性能条件 |
| 恶劣天气 | `bad_weather_config.json` | 雾雨条件测试 |
| 夜间场景 | `night_scenario_config.json` | 低照度条件 |
| 多目标 | `multi_target_config.json` | 复杂对抗场景 |

### 我只需要特定类型的输出
| 输出需求 | 配置文件 | 输出内容 |
|---------|---------|---------|
| 仅图像 | `image_only_config.json` | 640×480 PNG图像 |
| 仅视频 | `video_only_config.json` | 30fps MP4视频 |
| 仅数据 | `data_only_config.json` | CSV/JSON参数文件 |

### 我想进行系统测试
| 测试目的 | 配置文件 | 说明 |
|---------|---------|------|
| 性能测试 | `performance_test_config.json` | 标准性能基准 |
| 最小配置 | `minimal_config.json` | 最低资源需求 |
| 完整功能 | `complete_config.json` | 所有功能演示 |
## 🎯 使用建议

### 初学者路径
1. `quick_test_config.json` - 快速验证系统
2. `basic_config.json` - 了解基本功能
3. `infrared_target_config.json` - 学习设备配置
4. `clear_weather_config.json` - 理解环境影响

### 研究者路径
1. `data_only_config.json` - 获取大量参数数据
2. `performance_test_config.json` - 性能基准测试
3. `complete_config.json` - 完整功能验证
4. 自定义配置 - 根据研究需求定制

### 演示者路径
1. `image_only_config.json` - 生成演示图像
2. `video_only_config.json` - 制作演示视频
3. `night_scenario_config.json` - 展示特殊场景
4. `multi_target_config.json` - 复杂场景演示

## 🔧 自定义配置

### 基于现有模板修改
```bash
# 1. 复制最接近的模板
cp PhoElec/config_templates/basic_config.json my_custom_config.json

# 2. 编辑配置文件
# 修改参数值、添加设备、调整环境等

# 3. 验证配置
python -c "from PhoElec.utils.config_validator import validate_config_file; print(validate_config_file('my_custom_config.json'))"

# 4. 运行自定义配置
python run_simulation.py my_custom_config.json
```

### 组合多个模板
可以从不同模板中提取设备配置，组合成新的场景：
- 从 `infrared_target_config.json` 复制红外目标
- 从 `jammer_config.json` 复制干扰设备
- 从 `bad_weather_config.json` 复制环境设置

## ⚠️ 注意事项

1. **资源需求**: 复杂配置需要更多CPU和内存
2. **执行时间**: 大型配置可能需要较长时间
3. **存储空间**: 视频输出会占用较多磁盘空间
4. **参数合理性**: 确保参数值在合理范围内

## 🆘 故障排除

### 配置文件错误
```bash
# 验证JSON格式
python -m json.tool PhoElec/config_templates/your_config.json

# 验证参数合理性
python -c "from PhoElec.utils.config_validator import validate_config_file; print(validate_config_file('PhoElec/config_templates/your_config.json'))"
```

### 性能问题
```bash
# 减少线程数
python run_simulation.py PhoElec/config_templates/complete_config.json --threads 2

# 启用详细日志
python run_simulation.py PhoElec/config_templates/basic_config.json --log-level DEBUG
```

### 内存不足
- 使用 `data_only_config.json` 避免图像生成
- 减少 `data_count` 参数值
- 使用 `minimal_config.json` 进行测试

---

**配置模板总数**: 18个  
**覆盖场景**: 基础测试、设备专用、环境场景、输出专用、性能测试 

{"simulation": {"scenario_name": "完整参数配置示例", "duration": 300.0, "time_step": 0.05, "data_count": 2000, "output_types": ["static_images", "dynamic_images", "parameters"], "environment": {"weather_condition": "clear_weather", "temperature": 293.15, "humidity": 0.65, "pressure": 101325, "wind_speed": 8.0, "visibility": 20000, "cloud_cover": 0.2, "precipitation": 0.0, "atmospheric_turbulence": 0.3}}, "system": {"max_threads": 8, "image_resolution": [640, 480], "video_fps": 30, "random_seed": 42, "memory_limit_mb": 4096, "cpu_limit_percent": 80, "log_level": "INFO", "enable_performance_monitoring": true}, "optical_targets": [{"model": "高级红外目标设备_A型", "position": {"latitude": 39.9042, "longitude": 116.4074, "altitude": 1500.0}, "observation_direction": {"azimuth": 45.0, "elevation": 15.0, "scan_range_azimuth": 60.0, "scan_range_elevation": 30.0}, "performance_params": {"detection_range": 25000, "resolution": 0.05, "field_of_view": 15.0, "spectral_range": [3e-06, 5e-06], "sensitivity": 0.9, "noise_equivalent_temperature": 0.05, "frame_rate": 50, "pixel_count": [640, 480]}, "work_mode": "active_tracking", "temperature": {"engine": 500.0, "body": 320.0, "exhaust": 800.0, "background": 285.0}, "component_areas": {"engine": 3.0, "body": 60.0, "exhaust": 1.5}, "emissivity": {"engine": 0.85, "body": 0.9, "exhaust": 0.95, "background": 0.9}, "temperature_variation": 15.0, "variation_period": 120.0}], "optical_jammers": [{"model": "多功能光电干扰系统_X1", "position": {"latitude": 39.9, "longitude": 116.4, "altitude": 800.0}, "jamming_direction": {"azimuth": 90.0, "elevation": 5.0, "beam_width_azimuth": 30.0, "beam_width_elevation": 15.0}, "performance_params": {"jamming_power": 2000, "jamming_frequency": 1000, "coverage_range": 8000, "wavelength": 5.32e-07, "beam_divergence": 0.005, "pulse_duration": 1e-06, "duty_cycle": 0.3, "modulation_frequency": 100, "polarization": "linear"}, "work_mode": "adaptive_jamming", "jamming_strategy": "multi_spectral_interference", "jamming_patterns": [{"type": "continuous_wave", "power_ratio": 0.4, "frequency_range": [500, 1500]}, {"type": "pulsed", "power_ratio": 0.6, "pulse_width": 1e-06, "repetition_rate": 1000}]}], "optical_recons": [{"model": "智能光电侦察系统_R2", "position": {"latitude": 39.92, "longitude": 116.42, "altitude": 2000.0}, "performance_params": {"detection_range": 30000, "resolution": 0.02, "spectral_coverage": [3e-07, 1.5e-05], "sensitivity": 0.95, "field_of_view": 25.0, "analysis_bandwidth": 2000000, "processing_delay": 0.1, "false_alarm_rate": 0.01, "detection_probability": 0.95}, "work_mode": "intelligent_surveillance", "detection_mode": "multi_spectral_analysis", "detection_algorithms": [{"name": "spectral_signature_matching", "enabled": true, "threshold": 0.8, "confidence_weight": 0.4}, {"name": "motion_detection", "enabled": true, "threshold": 0.7, "confidence_weight": 0.3}, {"name": "thermal_anomaly_detection", "enabled": true, "threshold": 0.75, "confidence_weight": 0.3}], "tracking_params": {"max_targets": 10, "track_initiation_threshold": 3, "track_termination_threshold": 5, "position_accuracy": 1.0, "velocity_accuracy": 0.5}}]}
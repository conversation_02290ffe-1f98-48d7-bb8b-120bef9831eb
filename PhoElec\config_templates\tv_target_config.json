{"simulation": {"scenario_name": "电视目标专用仿真", "duration": 150.0, "time_step": 0.1, "data_count": 600, "output_types": ["static_images", "dynamic_images", "parameters"], "environment": {"weather_condition": "clear_weather", "temperature": 295.15, "humidity": 0.5, "pressure": 101325, "wind_speed": 4.0, "visibility": 22000, "illumination": "daylight"}}, "system": {"max_threads": 4, "image_resolution": [640, 480], "video_fps": 30, "random_seed": 4567}, "optical_targets": [{"model": "电视目标_可见光CCD", "position": {"latitude": 39.9042, "longitude": 116.4074, "altitude": 1000.0}, "observation_direction": {"azimuth": 0.0, "elevation": 0.0}, "performance_params": {"detection_range": 15000, "resolution": 0.15, "field_of_view": 20.0, "spectral_range": [4e-07, 7e-07], "sensitivity": 0.9, "pixel_size": 5e-06, "frame_rate": 25, "dynamic_range": 60}, "work_mode": "passive_observation", "sensor_params": {"sensor_type": "CCD", "pixel_count": [640, 480], "fill_factor": 0.8, "quantum_efficiency": 0.85, "read_noise": 8, "dark_current": 1e-12, "full_well_capacity": 100000}, "optical_params": {"focal_length": 0.1, "f_number": 2.8, "aperture_diameter": 0.036, "optical_transmission": 0.9}}, {"model": "电视目标_低照度CMOS", "position": {"latitude": 39.91, "longitude": 116.41, "altitude": 1500.0}, "observation_direction": {"azimuth": 90.0, "elevation": 10.0}, "performance_params": {"detection_range": 12000, "resolution": 0.2, "field_of_view": 25.0, "spectral_range": [3.5e-07, 9e-07], "sensitivity": 0.95, "minimum_illumination": 0.001}, "work_mode": "low_light_surveillance", "sensor_params": {"sensor_type": "CMOS", "pixel_count": [640, 480], "pixel_size": 7e-06, "quantum_efficiency": 0.9, "read_noise": 5, "gain_range": [1, 1000]}}]}
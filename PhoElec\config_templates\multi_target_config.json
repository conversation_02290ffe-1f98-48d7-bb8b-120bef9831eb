{"simulation": {"scenario_name": "多目标光电对抗场景", "duration": 10.0, "time_step": 0.1, "data_count": 3, "output_types": ["static_images", "dynamic_images", "parameters"], "environment": {"weather_condition": "clear_weather", "temperature": 291.15, "humidity": 0.6, "pressure": 101325, "wind_speed": 5.0, "visibility": 25000}}, "system": {"max_threads": 8, "image_resolution": [640, 480], "video_fps": 30, "random_seed": 1234}, "optical_targets": [{"model": "多目标_红外目标A", "position": {"latitude": 39.9042, "longitude": 116.4074, "altitude": 1500.0}, "observation_direction": {"azimuth": 45.0, "elevation": 10.0}, "performance_params": {"detection_range": 20000, "resolution": 0.06, "field_of_view": 15.0}, "work_mode": "coordinated_search", "temperature": {"engine": 520.0, "body": 305.0, "background": 291.15}}, {"model": "多目标_红外目标B", "position": {"latitude": 39.91, "longitude": 116.415, "altitude": 1200.0}, "observation_direction": {"azimuth": 90.0, "elevation": 5.0}, "performance_params": {"detection_range": 18000, "resolution": 0.08, "field_of_view": 12.0}, "work_mode": "coordinated_search", "temperature": {"engine": 480.0, "body": 300.0, "background": 291.15}}, {"model": "多目标_激光目标C", "position": {"latitude": 39.9, "longitude": 116.42, "altitude": 1800.0}, "observation_direction": {"azimuth": 135.0, "elevation": 15.0}, "performance_params": {"detection_range": 25000, "resolution": 0.03, "field_of_view": 8.0, "wavelength": 1.064e-06, "power": 1800}, "work_mode": "active_illumination"}, {"model": "多目标_电视目标D", "position": {"latitude": 39.895, "longitude": 116.41, "altitude": 1000.0}, "observation_direction": {"azimuth": 180.0, "elevation": 8.0}, "performance_params": {"detection_range": 15000, "resolution": 0.12, "field_of_view": 20.0, "spectral_range": [4e-07, 7e-07]}, "work_mode": "passive_observation"}], "optical_jammers": [{"model": "多目标_烟幕干扰1", "position": {"latitude": 39.902, "longitude": 116.402, "altitude": 500.0}, "jamming_direction": {"azimuth": 60.0, "elevation": 0.0}, "performance_params": {"coverage_range": 4000, "duration": 300, "coverage_radius": 150}, "work_mode": "area_denial", "jamming_strategy": "coordinated_obscuration"}, {"model": "多目标_激光干扰2", "position": {"latitude": 39.908, "longitude": 116.408, "altitude": 800.0}, "jamming_direction": {"azimuth": 120.0, "elevation": 12.0}, "performance_params": {"jamming_power": 3000, "jamming_frequency": 1200, "coverage_range": 12000, "wavelength": 5.32e-07}, "work_mode": "active_dazzling", "jamming_strategy": "sensor_overload"}], "optical_recons": [{"model": "多目标_侦察系统1", "position": {"latitude": 39.92, "longitude": 116.42, "altitude": 2500.0}, "performance_params": {"detection_range": 35000, "resolution": 0.02, "spectral_coverage": [3e-06, 1.2e-05], "sensitivity": 0.96, "field_of_view": 30.0, "multi_target_capacity": 15}, "work_mode": "multi_target_tracking", "detection_mode": "infrared_warning"}, {"model": "多目标_侦察系统2", "position": {"latitude": 39.925, "longitude": 116.425, "altitude": 2200.0}, "performance_params": {"detection_range": 30000, "resolution": 0.025, "spectral_coverage": [4e-07, 1.7e-06], "sensitivity": 0.94, "field_of_view": 25.0, "multi_target_capacity": 12}, "work_mode": "coordinated_surveillance", "detection_mode": "laser_warning"}]}
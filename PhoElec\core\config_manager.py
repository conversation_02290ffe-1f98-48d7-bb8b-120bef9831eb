"""
配置管理模块
负责解析和验证JSON配置文件，提供统一的配置访问接口
"""

import json
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from pathlib import Path


logger = logging.getLogger(__name__)


@dataclass
class DeviceConfig:
    """设备配置基类"""
    model: str
    position: Dict[str, float]  # {"latitude": float, "longitude": float, "altitude": float}
    performance_params: Dict[str, Any]
    work_mode: str


@dataclass
class OpticalTargetConfig(DeviceConfig):
    """光电目标设备配置"""
    observation_direction: Dict[str, float]  # {"azimuth": float, "elevation": float}


@dataclass
class OpticalJammerConfig(DeviceConfig):
    """光电干扰设备配置"""
    jamming_direction: Dict[str, float]  # {"azimuth": float, "elevation": float}
    jamming_strategy: str


@dataclass
class OpticalReconConfig(DeviceConfig):
    """光电侦察设备配置"""
    detection_mode: str


@dataclass
class SimulationConfig:
    """仿真配置"""
    scenario_name: str
    duration: float  # 仿真时长(秒)
    time_step: float  # 时间步长(秒)
    data_count: int  # 数据生成数量
    output_types: List[str]  # 输出类型列表
    environment: Dict[str, Any]  # 环境参数


@dataclass
class SystemConfig:
    """系统配置"""
    max_threads: int = 4
    image_resolution: tuple = (640, 480)
    video_fps: int = 30
    random_seed: Optional[int] = None


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_data: Dict[str, Any]):
        """
        初始化配置管理器
        
        Args:
            config_data: 配置数据字典
        """
        self.raw_config = config_data
        self._parse_config()
    
    def _parse_config(self):
        """解析配置数据"""
        try:
            # 解析仿真配置
            sim_config = self.raw_config.get('simulation', {})
            self.simulation = SimulationConfig(
                scenario_name=sim_config.get('scenario_name', 'default_scenario'),
                duration=sim_config.get('duration', 60.0),
                time_step=sim_config.get('time_step', 0.1),
                data_count=min(sim_config.get('data_count', 1000), 5000),  # 限制最大5000
                output_types=sim_config.get('output_types', ['images', 'parameters']),
                environment=sim_config.get('environment', {})
            )
            
            # 解析系统配置
            sys_config = self.raw_config.get('system', {})
            self.system = SystemConfig(
                max_threads=sys_config.get('max_threads', 4),
                image_resolution=tuple(sys_config.get('image_resolution', [640, 480])),
                video_fps=sys_config.get('video_fps', 30),
                random_seed=sys_config.get('random_seed')
            )
            
            # 解析设备配置
            self.optical_targets = self._parse_optical_targets()
            self.optical_jammers = self._parse_optical_jammers()
            self.optical_recons = self._parse_optical_recons()
            
            logger.info("配置解析完成")
            
        except Exception as e:
            logger.error(f"配置解析失败: {e}")
            raise ValueError(f"配置解析失败: {e}")
    
    def _parse_optical_targets(self) -> List[OpticalTargetConfig]:
        """解析光电目标设备配置"""
        targets = []
        target_configs = self.raw_config.get('optical_targets', [])
        
        for i, config in enumerate(target_configs):
            try:
                target = OpticalTargetConfig(
                    model=config.get('model', f'target_{i}'),
                    position=config.get('position', {}),
                    performance_params=config.get('performance_params', {}),
                    work_mode=config.get('work_mode', 'passive_search'),
                    observation_direction=config.get('observation_direction', {})
                )
                targets.append(target)
            except Exception as e:
                logger.warning(f"光电目标设备 {i} 配置解析失败: {e}")
        
        return targets
    
    def _parse_optical_jammers(self) -> List[OpticalJammerConfig]:
        """解析光电干扰设备配置"""
        jammers = []
        jammer_configs = self.raw_config.get('optical_jammers', [])
        
        for i, config in enumerate(jammer_configs):
            try:
                jammer = OpticalJammerConfig(
                    model=config.get('model', f'jammer_{i}'),
                    position=config.get('position', {}),
                    performance_params=config.get('performance_params', {}),
                    work_mode=config.get('work_mode', 'continuous'),
                    jamming_direction=config.get('jamming_direction', {}),
                    jamming_strategy=config.get('jamming_strategy', 'broadband')
                )
                jammers.append(jammer)
            except Exception as e:
                logger.warning(f"光电干扰设备 {i} 配置解析失败: {e}")
        
        return jammers
    
    def _parse_optical_recons(self) -> List[OpticalReconConfig]:
        """解析光电侦察设备配置"""
        recons = []
        recon_configs = self.raw_config.get('optical_recons', [])
        
        for i, config in enumerate(recon_configs):
            try:
                recon = OpticalReconConfig(
                    model=config.get('model', f'recon_{i}'),
                    position=config.get('position', {}),
                    performance_params=config.get('performance_params', {}),
                    work_mode=config.get('work_mode', 'passive_detection'),
                    detection_mode=config.get('detection_mode', 'infrared_warning')
                )
                recons.append(recon)
            except Exception as e:
                logger.warning(f"光电侦察设备 {i} 配置解析失败: {e}")
        
        return recons
    
    def validate(self):
        """验证配置有效性"""
        errors = []
        
        # 验证仿真配置
        if self.simulation.duration <= 0:
            errors.append("仿真时长必须大于0")
        
        if self.simulation.time_step <= 0:
            errors.append("时间步长必须大于0")
        
        if self.simulation.data_count <= 0:
            errors.append("数据生成数量必须大于0")
        
        if self.simulation.data_count > 5000:
            errors.append("数据生成数量不能超过5000")
        
        # 验证系统配置
        if self.system.max_threads <= 0:
            errors.append("最大线程数必须大于0")
        
        if len(self.system.image_resolution) != 2:
            errors.append("图像分辨率必须是2个数值的元组")
        
        # 验证设备配置
        if not self.optical_targets and not self.optical_jammers and not self.optical_recons:
            errors.append("至少需要配置一种设备")
        
        # 验证设备位置信息
        for i, target in enumerate(self.optical_targets):
            if not self._validate_position(target.position):
                errors.append(f"光电目标设备 {i} 位置信息不完整")
        
        for i, jammer in enumerate(self.optical_jammers):
            if not self._validate_position(jammer.position):
                errors.append(f"光电干扰设备 {i} 位置信息不完整")
        
        for i, recon in enumerate(self.optical_recons):
            if not self._validate_position(recon.position):
                errors.append(f"光电侦察设备 {i} 位置信息不完整")
        
        if errors:
            error_msg = "配置验证失败:\n" + "\n".join(f"  - {error}" for error in errors)
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        logger.info("配置验证通过")
    
    def _validate_position(self, position: Dict[str, float]) -> bool:
        """验证位置信息"""
        required_keys = ['latitude', 'longitude', 'altitude']
        return all(key in position for key in required_keys)
    
    def get_device_count(self) -> Dict[str, int]:
        """获取各类设备数量"""
        return {
            'optical_targets': len(self.optical_targets),
            'optical_jammers': len(self.optical_jammers),
            'optical_recons': len(self.optical_recons)
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'simulation': self.simulation.__dict__,
            'system': self.system.__dict__,
            'device_count': self.get_device_count()
        }

"""
并行处理工具模块
提供多线程和多进程处理功能，优化计算密集型任务的执行效率
"""

import threading
import multiprocessing
import concurrent.futures
import queue
import time
import logging
from typing import Callable, List, Any, Dict, Optional, Tuple
from functools import wraps
import numpy as np

from .logger import LoggerMixin, log_execution_time


logger = logging.getLogger(__name__)


class ThreadSafeCounter:
    """线程安全计数器"""
    
    def __init__(self, initial_value: int = 0):
        self._value = initial_value
        self._lock = threading.Lock()
    
    def increment(self, amount: int = 1) -> int:
        """增加计数"""
        with self._lock:
            self._value += amount
            return self._value
    
    def decrement(self, amount: int = 1) -> int:
        """减少计数"""
        with self._lock:
            self._value -= amount
            return self._value
    
    @property
    def value(self) -> int:
        """获取当前值"""
        with self._lock:
            return self._value
    
    def reset(self, value: int = 0):
        """重置计数"""
        with self._lock:
            self._value = value


class ProgressTracker(LoggerMixin):
    """进度跟踪器"""
    
    def __init__(self, total_tasks: int, log_interval: int = 100):
        """
        初始化进度跟踪器
        
        Args:
            total_tasks: 总任务数
            log_interval: 日志记录间隔
        """
        self.total_tasks = total_tasks
        self.log_interval = log_interval
        self.completed_tasks = ThreadSafeCounter()
        self.failed_tasks = ThreadSafeCounter()
        self.start_time = time.time()
        self._lock = threading.Lock()
    
    def update_progress(self, success: bool = True):
        """更新进度"""
        if success:
            completed = self.completed_tasks.increment()
        else:
            self.failed_tasks.increment()
            completed = self.completed_tasks.value
        
        if completed % self.log_interval == 0 or completed == self.total_tasks:
            self._log_progress(completed)
    
    def _log_progress(self, completed: int):
        """记录进度日志"""
        elapsed_time = time.time() - self.start_time
        progress_percent = (completed / self.total_tasks) * 100
        
        if completed > 0:
            avg_time_per_task = elapsed_time / completed
            estimated_remaining = (self.total_tasks - completed) * avg_time_per_task
        else:
            estimated_remaining = 0
        
        self.logger.info(
            f"进度: {completed}/{self.total_tasks} ({progress_percent:.1f}%) "
            f"已用时: {elapsed_time:.1f}s 预计剩余: {estimated_remaining:.1f}s "
            f"失败: {self.failed_tasks.value}"
        )
    
    def get_summary(self) -> Dict[str, Any]:
        """获取进度摘要"""
        elapsed_time = time.time() - self.start_time
        return {
            'total_tasks': self.total_tasks,
            'completed_tasks': self.completed_tasks.value,
            'failed_tasks': self.failed_tasks.value,
            'success_rate': self.completed_tasks.value / self.total_tasks if self.total_tasks > 0 else 0,
            'elapsed_time': elapsed_time,
            'tasks_per_second': self.completed_tasks.value / elapsed_time if elapsed_time > 0 else 0
        }


class ParallelProcessor(LoggerMixin):
    """并行处理器"""
    
    def __init__(self, max_workers: Optional[int] = None, use_processes: bool = False):
        """
        初始化并行处理器
        
        Args:
            max_workers: 最大工作线程/进程数
            use_processes: 是否使用多进程（默认使用多线程）
        """
        if max_workers is None:
            max_workers = min(multiprocessing.cpu_count(), 8)
        
        self.max_workers = max_workers
        self.use_processes = use_processes
        self.logger.info(f"初始化并行处理器: {max_workers} {'进程' if use_processes else '线程'}")
    
    @log_execution_time
    def process_batch(
        self,
        func: Callable,
        tasks: List[Any],
        progress_callback: Optional[Callable] = None,
        chunk_size: Optional[int] = None
    ) -> List[Any]:
        """
        批量处理任务
        
        Args:
            func: 处理函数
            tasks: 任务列表
            progress_callback: 进度回调函数
            chunk_size: 分块大小
            
        Returns:
            处理结果列表
        """
        if not tasks:
            return []
        
        # 设置分块大小
        if chunk_size is None:
            chunk_size = max(1, len(tasks) // (self.max_workers * 4))
        
        # 创建进度跟踪器
        progress_tracker = ProgressTracker(len(tasks))
        
        results = []
        
        # 选择执行器类型
        executor_class = (concurrent.futures.ProcessPoolExecutor if self.use_processes 
                         else concurrent.futures.ThreadPoolExecutor)
        
        try:
            with executor_class(max_workers=self.max_workers) as executor:
                # 提交任务
                future_to_task = {}
                
                for i, task in enumerate(tasks):
                    future = executor.submit(self._safe_execute, func, task, i)
                    future_to_task[future] = (task, i)
                
                # 收集结果
                for future in concurrent.futures.as_completed(future_to_task):
                    task, task_index = future_to_task[future]
                    
                    try:
                        result = future.result()
                        results.append((task_index, result))
                        progress_tracker.update_progress(success=True)
                        
                        if progress_callback:
                            progress_callback(task_index, result, None)
                            
                    except Exception as e:
                        self.logger.error(f"任务 {task_index} 执行失败: {e}")
                        results.append((task_index, None))
                        progress_tracker.update_progress(success=False)
                        
                        if progress_callback:
                            progress_callback(task_index, None, e)
        
        except Exception as e:
            self.logger.error(f"并行处理失败: {e}")
            raise
        
        # 按原始顺序排序结果
        results.sort(key=lambda x: x[0])
        final_results = [result for _, result in results]
        
        # 记录摘要
        summary = progress_tracker.get_summary()
        self.logger.info(f"批量处理完成: {summary}")
        
        return final_results
    
    def _safe_execute(self, func: Callable, task: Any, task_index: int) -> Any:
        """安全执行任务"""
        try:
            return func(task)
        except Exception as e:
            self.logger.error(f"任务 {task_index} 执行异常: {e}")
            raise
    
    def process_chunks(
        self,
        func: Callable,
        data: List[Any],
        chunk_size: int,
        combine_func: Optional[Callable] = None
    ) -> Any:
        """
        分块处理数据
        
        Args:
            func: 处理函数
            data: 数据列表
            chunk_size: 分块大小
            combine_func: 结果合并函数
            
        Returns:
            处理结果
        """
        if not data:
            return [] if combine_func is None else combine_func([])
        
        # 分块
        chunks = [data[i:i + chunk_size] for i in range(0, len(data), chunk_size)]
        
        # 并行处理分块
        chunk_results = self.process_batch(func, chunks)
        
        # 合并结果
        if combine_func:
            return combine_func(chunk_results)
        else:
            # 默认合并方式：展平列表
            result = []
            for chunk_result in chunk_results:
                if isinstance(chunk_result, list):
                    result.extend(chunk_result)
                else:
                    result.append(chunk_result)
            return result


class AsyncTaskManager(LoggerMixin):
    """异步任务管理器"""
    
    def __init__(self, max_concurrent_tasks: int = 10):
        """
        初始化异步任务管理器
        
        Args:
            max_concurrent_tasks: 最大并发任务数
        """
        self.max_concurrent_tasks = max_concurrent_tasks
        self.task_queue = queue.Queue()
        self.result_queue = queue.Queue()
        self.active_tasks = ThreadSafeCounter()
        self.completed_tasks = ThreadSafeCounter()
        self.failed_tasks = ThreadSafeCounter()
        self._shutdown = threading.Event()
        self._workers = []
        
        # 启动工作线程
        for i in range(max_concurrent_tasks):
            worker = threading.Thread(target=self._worker_loop, name=f"AsyncWorker-{i}")
            worker.daemon = True
            worker.start()
            self._workers.append(worker)
        
        self.logger.info(f"异步任务管理器启动，工作线程数: {max_concurrent_tasks}")
    
    def submit_task(self, func: Callable, *args, **kwargs) -> str:
        """
        提交异步任务
        
        Args:
            func: 任务函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            任务ID
        """
        task_id = f"task_{int(time.time() * 1000000)}"
        task = {
            'id': task_id,
            'func': func,
            'args': args,
            'kwargs': kwargs,
            'submit_time': time.time()
        }
        
        self.task_queue.put(task)
        return task_id
    
    def get_result(self, timeout: Optional[float] = None) -> Optional[Dict[str, Any]]:
        """
        获取任务结果
        
        Args:
            timeout: 超时时间
            
        Returns:
            任务结果字典
        """
        try:
            return self.result_queue.get(timeout=timeout)
        except queue.Empty:
            return None
    
    def _worker_loop(self):
        """工作线程循环"""
        while not self._shutdown.is_set():
            try:
                task = self.task_queue.get(timeout=1.0)
                self._execute_task(task)
                self.task_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                self.logger.error(f"工作线程异常: {e}")
    
    def _execute_task(self, task: Dict[str, Any]):
        """执行任务"""
        task_id = task['id']
        self.active_tasks.increment()
        
        try:
            start_time = time.time()
            result = task['func'](*task['args'], **task['kwargs'])
            execution_time = time.time() - start_time
            
            self.result_queue.put({
                'task_id': task_id,
                'success': True,
                'result': result,
                'execution_time': execution_time,
                'submit_time': task['submit_time']
            })
            
            self.completed_tasks.increment()
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            self.result_queue.put({
                'task_id': task_id,
                'success': False,
                'error': str(e),
                'execution_time': execution_time,
                'submit_time': task['submit_time']
            })
            
            self.failed_tasks.increment()
            self.logger.error(f"任务 {task_id} 执行失败: {e}")
        
        finally:
            self.active_tasks.decrement()
    
    def shutdown(self, wait: bool = True):
        """关闭任务管理器"""
        self._shutdown.set()
        
        if wait:
            # 等待所有任务完成
            self.task_queue.join()
            
            # 等待工作线程结束
            for worker in self._workers:
                worker.join(timeout=5.0)
        
        self.logger.info("异步任务管理器已关闭")
    
    def get_status(self) -> Dict[str, Any]:
        """获取状态信息"""
        return {
            'active_tasks': self.active_tasks.value,
            'completed_tasks': self.completed_tasks.value,
            'failed_tasks': self.failed_tasks.value,
            'pending_tasks': self.task_queue.qsize(),
            'pending_results': self.result_queue.qsize()
        }


def parallel_map(func: Callable, iterable: List[Any], max_workers: Optional[int] = None) -> List[Any]:
    """
    并行映射函数（简化接口）
    
    Args:
        func: 映射函数
        iterable: 可迭代对象
        max_workers: 最大工作线程数
        
    Returns:
        结果列表
    """
    processor = ParallelProcessor(max_workers=max_workers)
    return processor.process_batch(func, list(iterable))


def chunked_parallel_map(
    func: Callable, 
    iterable: List[Any], 
    chunk_size: int, 
    max_workers: Optional[int] = None
) -> List[Any]:
    """
    分块并行映射函数
    
    Args:
        func: 映射函数
        iterable: 可迭代对象
        chunk_size: 分块大小
        max_workers: 最大工作线程数
        
    Returns:
        结果列表
    """
    processor = ParallelProcessor(max_workers=max_workers)
    
    def process_chunk(chunk):
        return [func(item) for item in chunk]
    
    return processor.process_chunks(process_chunk, list(iterable), chunk_size)

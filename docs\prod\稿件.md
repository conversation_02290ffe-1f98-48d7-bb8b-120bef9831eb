### 1. 性能指标达成

#### 1.1 光电目标数据产生
- ✅ 自定义光电设备 ≥ 3 类：红外、激光、电视
- ✅ 性能参数 ≥ 3 类：探测距离、分辨率、视场角
- ✅ 工作模式 ≥ 2 类：被动搜索、主动照射
- ✅ 输出数据类型 ≥ 2 类：状态参数、图片/视频

#### 1.2 光电干扰设备数据产生
- ✅ 干扰设备类型 ≥ 2 类：烟幕、红外诱饵弹、激光致盲
- ✅ 性能参数 ≥ 3 类：干扰功率、干扰频段、干扰策略

#### 1.3 光电对抗侦察设备数据产生
- ✅ 侦察设备模型 ≥ 2 类：红外侦测系统、光电信号分析器
- ✅ 性能参数 ≥ 3 类：探测距离、分辨率、光谱覆盖范围
- ✅ 工作模式 ≥ 2 类：激光告警、远红外告警

## 2. 三个核心算法分析

```mermaid
graph TD
    A[配置解析与验证] --> B[初始化仿真引擎]
    B --> C[创建三类设备仿真器]
    C --> D[配置线程池执行器]
    D --> E[并行提交仿真任务]

    E --> F1[目标仿真任务]
    E --> F2[干扰仿真任务]
    E --> F3[侦察仿真任务]

    F1 --> G1[物理建模计算]
    F2 --> G2[干扰效果计算]
    F3 --> G3[检测识别计算]

    G1 --> H[收集所有仿真结果]
    G2 --> H
    G3 --> H

    H --> I[执行干扰效果分析]
    I --> J[生成综合报告]
    J --> K[输出结果文件]

    style A fill:#E1F5FE
    style K fill:#F3E5F5
    style G1 fill:#E8F5E8
    style G2 fill:#FFF3E0
    style G3 fill:#F1F8E9
```

### 2.1 目标算法实现逻辑

#### 2.1.1 目标设备算法流程
```mermaid
flowchart TD
    A[初始化目标配置] --> B[构建物理模型]
    B --> C[辐射模型<br/>TargetRadiation]
    B --> D[大气模型<br/>AtmosphericTransmission]
    B --> E[传感器模型<br/>ImagingSensor]

    C --> F[场景参数生成]
    D --> F
    E --> F

    F --> G{输出类型}
    G -->|图像| H[图像生成流程]
    G -->|视频| I[视频生成流程]
    G -->|数据| J[参数数据生成]

    H --> K[计算辐射强度<br/>黑体辐射+组件温度]
    K --> L[大气传输衰减<br/>Beer-Lambert定律]
    L --> M[传感器响应<br/>光电转换+噪声]
    M --> N[图像合成+标注]

    I --> O[动态参数变化<br/>温度+运动轨迹]
    O --> P[逐帧图像生成]
    P --> Q[视频编码输出]

    J --> R[性能参数计算<br/>偏离/准确率/距离/概率]

    N --> S[输出文件]
    Q --> S
    R --> S

    style A fill:#E3F2FD
    style S fill:#F3E5F5
    style K fill:#E8F5E8
    style L fill:#FFF3E0
    style M fill:#F1F8E9
```

#### 2.1.2 核心算法步骤
1. **物理建模初始化**：
   - 辐射模型：基于目标类型配置温度分布图和发射率
   - 大气模型：根据天气条件设置消光系数
   - 传感器模型：配置量子效率、像素参数、光学系统
2. **场景参数生成**：随机生成目标距离、方位角、俯仰角、环境条件
3. **辐射强度计算**：
   - 应用普朗克函数计算黑体辐射
   - 基于组件温度和面积计算总辐射强度
   - 考虑发射率和立体角修正
4. **大气传输建模**：
   - 应用Beer-Lambert定律计算散射衰减
   - 计算分子吸收（水蒸气、CO2）
   - 综合得到总透射率
5. **传感器响应计算**：
   - 光电转换：计算光电流和信号电荷
   - 噪声建模：散粒噪声、热噪声、读出噪声
   - 信噪比计算和饱和检查
6. **输出生成**：
   - 图像：目标成像+噪声+中文标注
   - 视频：动态参数变化+逐帧生成
   - 数据：偏离范围、识别准确率、探测距离、探测概率

### 2.2 干扰算法实现逻辑

#### 2.2.1 干扰效果算法流程
```mermaid
flowchart TD
    A[初始化干扰配置] --> B[设备类型识别]
    B --> C{干扰类型}

    C -->|烟幕| D[烟幕模型<br/>覆盖半径+密度]
    C -->|诱饵| E[诱饵模型<br/>辐射强度+光谱]
    C -->|激光| F[激光模型<br/>功率密度+发散角]
    C -->|其他| G[通用模型<br/>功率衰减]

    D --> H[基础效果计算]
    E --> H
    F --> H
    G --> H

    H --> I[环境影响修正]
    I --> J[天气因子<br/>风速/雨雾影响]
    I --> K[大气因子<br/>传输衰减]
    I --> L[目标因子<br/>易感性/距离]

    J --> M[综合效果评估]
    K --> M
    L --> M

    M --> N[多维参数生成]
    N --> O[干扰效果数据]
    N --> P[功耗特性数据]
    N --> Q[覆盖范围数据]
    N --> R[持续时间数据]

    O --> S[输出数据文件]
    P --> S
    Q --> S
    R --> S

    style A fill:#E3F2FD
    style S fill:#F3E5F5
    style H fill:#E8F5E8
    style M fill:#FFF3E0
```

#### 2.2.2 核心算法步骤
1. **设备类型自动识别**：
   - 基于设备型号字符串匹配识别类型
   - 支持烟幕、红外诱饵、激光致盲、箔条等类型
2. **物理模型初始化**：
   - 烟幕：覆盖半径、密度衰减模型
   - 诱饵：黑体辐射模型、光谱特性
   - 激光：高斯光束传播、功率密度分布
3. **基础效果计算**：
   - 烟幕：基于距离和覆盖半径的遮蔽效果
   - 诱饵：基于辐射强度和距离平方反比的诱骗效果
   - 激光：基于功率密度的致盲/损伤效果
4. **环境影响建模**：
   - 天气因子：风速对烟幕、雨雾对激光的影响
   - 大气传输：应用大气透射率模型
   - 目标特性：易感性、防护能力评估
5. **多维参数生成**：
   - 干扰效果：最终干扰成功率
   - 功耗特性：工作模式、温度影响、效率损失
   - 覆盖范围：有效作用距离和角度
   - 持续时间：环境影响下的工作时长

### 2.3 侦察算法实现逻辑

#### 2.3.1 侦察处理算法流程
```mermaid
flowchart TD
    A[初始化侦察配置] --> B[设备类型识别]
    B --> C[传感器和探测器初始化]
    C --> D[ImagingSensor<br/>PhotoDetector]

    D --> E[信号检测处理]
    E --> F[信号强度计算<br/>噪声水平评估]
    F --> G[信噪比计算<br/>阈值判决]
    G --> H{检测结果}
    H -->|命中| I[目标检测成功]
    H -->|虚警| J[虚警控制]
    H -->|漏检| K[漏检分析]

    I --> L[特征提取处理]
    J --> L
    K --> L

    L --> M[多维特征提取]
    M --> N[光谱特征<br/>空间特征<br/>时间特征<br/>偏振特征]
    N --> O[特征质量评估<br/>置信度计算]

    O --> P[目标跟踪处理]
    P --> Q[运动参数估计<br/>跟踪精度计算]
    Q --> R[跟踪状态管理<br/>获取/跟踪/丢失]

    R --> S[识别分类处理]
    S --> T[目标类型识别<br/>置信度评估]
    T --> U[环境因素补偿<br/>距离影响修正]

    U --> V[性能参数生成]
    V --> W[探测距离<br/>发现概率<br/>识别准确率]

    W --> X[输出数据文件]

    style A fill:#E3F2FD
    style X fill:#F3E5F5
    style G fill:#E8F5E8
    style O fill:#FFF3E0
    style U fill:#F1F8E9
```

#### 2.3.2 核心算法步骤
1. **设备类型自动识别**：
   - 基于检测模式识别设备类型（红外/激光/光电/光谱）
   - 初始化对应的传感器和探测器模型
   - 配置探测器参数（量子效率、暗电流、光谱范围）
2. **信号检测与初筛**：
   - 目标存在性模拟（30%概率存在目标）
   - 信号强度计算（基于目标存在性）
   - 噪声水平建模（0.05-0.2范围）
   - 信噪比计算和阈值判决（阈值0.7）
   - 检测结果分类（命中/虚警/漏检/正确拒绝）
3. **多维特征提取**：
   - 光谱特征：基于探测器光谱范围的特征提取
   - 空间特征：目标形状、大小、位置信息
   - 时间特征：运动模式、变化趋势分析
   - 偏振特征：偏振状态分析（质量较低）
   - 特征质量评估和整体置信度计算
4. **目标跟踪算法**：
   - 运动参数估计：速度（10-300 m/s）、方向（0-360°）
   - 跟踪精度计算：基于速度和距离的精度衰减模型
   - 位置和速度误差建模
   - 跟踪状态管理：获取/跟踪/丢失/惯性四种状态
5. **识别分类算法**：
   - 目标类型识别：飞机/导弹/车辆/舰船/未知
   - 识别置信度评估：基于正确识别的置信度模型
   - 距离影响因子：远距离识别精度衰减
   - 环境因素补偿：天气、目标尺寸等影响修正
6. **性能参数计算**：
   - 探测距离：基础距离+环境影响+目标特性+传感器性能
   - 发现概率：距离分段概率模型+环境修正
   - 识别准确率：多因子综合评估模型
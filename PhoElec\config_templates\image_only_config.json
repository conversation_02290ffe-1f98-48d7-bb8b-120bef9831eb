{"simulation": {"scenario_name": "仅图像输出场景", "duration": 90.0, "time_step": 0.1, "data_count": 200, "output_types": ["static_images"], "environment": {"weather_condition": "clear_weather", "temperature": 288.15, "humidity": 0.5, "pressure": 101325, "wind_speed": 3.0, "visibility": 23000}}, "system": {"max_threads": 4, "image_resolution": [640, 480], "video_fps": 30, "random_seed": 11111}, "optical_targets": [{"model": "图像专用_红外目标", "position": {"latitude": 39.9042, "longitude": 116.4074, "altitude": 1000.0}, "observation_direction": {"azimuth": 0.0, "elevation": 0.0}, "performance_params": {"detection_range": 15000, "resolution": 0.1, "field_of_view": 10.0, "spectral_range": [3e-06, 5e-06], "sensitivity": 0.85}, "work_mode": "passive_search", "temperature": {"engine": 500.0, "body": 300.0, "exhaust": 700.0, "background": 288.15}, "image_enhancement": {"contrast_boost": 1.2, "brightness_adjustment": 0.1, "noise_reduction": 0.8, "edge_enhancement": true}}, {"model": "图像专用_可见光目标", "position": {"latitude": 39.91, "longitude": 116.41, "altitude": 1200.0}, "observation_direction": {"azimuth": 90.0, "elevation": 5.0}, "performance_params": {"detection_range": 12000, "resolution": 0.15, "field_of_view": 15.0, "spectral_range": [4e-07, 7e-07], "sensitivity": 0.9}, "work_mode": "passive_observation", "image_processing": {"color_enhancement": true, "dynamic_range_compression": 1.5, "sharpening_filter": 0.3, "histogram_equalization": true}}], "optical_jammers": [{"model": "图像专用_烟幕干扰", "position": {"latitude": 39.9, "longitude": 116.4, "altitude": 500.0}, "jamming_direction": {"azimuth": 45.0, "elevation": 0.0}, "performance_params": {"coverage_range": 3000, "duration": 180, "coverage_radius": 120, "visual_obscuration": 0.9, "infrared_obscuration": 0.7}, "work_mode": "visual_screening", "jamming_strategy": "image_degradation", "visual_effects": {"opacity_variation": 0.3, "particle_animation": true, "density_gradient": true, "color_tinting": [0.8, 0.8, 0.9]}}]}
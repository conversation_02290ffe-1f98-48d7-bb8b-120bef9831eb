#!/usr/bin/env python3
"""
光电对抗仿真系统主入口
支持命令行调用，传入JSON配置文件路径，进行光电对抗仿真计算
"""

import sys
import argparse
import json
import logging
import os
from datetime import datetime
from pathlib import Path

from core.config_manager import ConfigManager
from core.output_manager import OutputManager
from core.simulation_engine import SimulationEngine
from utils.logger import setup_logger


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='光电对抗仿真系统',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python main.py config.json
  python main.py --config config.json --output-dir ./results
        """
    )
    
    parser.add_argument(
        'config_file',
        help='配置文件路径 (JSON格式)'
    )
    
    parser.add_argument(
        '--output-dir',
        default=None,
        help='输出目录路径 (默认自动生成)'
    )
    
    parser.add_argument(
        '--log-level',
        choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
        default='INFO',
        help='日志级别 (默认: INFO)'
    )
    
    parser.add_argument(
        '--threads',
        type=int,
        default=None,
        help='并行线程数 (默认自动检测)'
    )
    
    return parser.parse_args()


def validate_config_file(config_path):
    """验证配置文件"""
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"配置文件不存在: {config_path}")
    
    if not config_path.endswith('.json'):
        raise ValueError("配置文件必须是JSON格式")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        return config
    except json.JSONDecodeError as e:
        raise ValueError(f"配置文件JSON格式错误: {e}")
    except Exception as e:
        raise ValueError(f"读取配置文件失败: {e}")


def main():
    """主函数"""
    try:
        # 解析命令行参数
        args = parse_arguments()
        
        # 验证配置文件
        config_data = validate_config_file(args.config_file)
        
        # 初始化输出管理器
        output_manager = OutputManager(base_dir=args.output_dir)
        output_dir = output_manager.create_session_dir()
        
        # 设置日志系统
        logger = setup_logger(
            log_level=args.log_level,
            log_file=os.path.join(output_dir, 'simulation.log')
        )
        
        logger.info("="*60)
        logger.info("光电对抗仿真系统启动")
        logger.info("="*60)
        logger.info(f"配置文件: {args.config_file}")
        logger.info(f"输出目录: {output_dir}")
        logger.info(f"日志级别: {args.log_level}")
        
        # 初始化配置管理器
        config_manager = ConfigManager(config_data)
        config_manager.validate()
        
        # 保存配置文件副本到输出目录
        config_backup_path = os.path.join(output_dir, 'config_backup.json')
        with open(config_backup_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, ensure_ascii=False, indent=2)
        logger.info(f"配置文件已备份到: {config_backup_path}")
        
        # 初始化仿真引擎
        simulation_engine = SimulationEngine(
            config_manager=config_manager,
            output_manager=output_manager,
            num_threads=args.threads
        )
        
        # 执行仿真
        logger.info("开始执行仿真...")
        start_time = datetime.now()
        
        results = simulation_engine.run()
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        # 输出结果统计
        logger.info("="*60)
        logger.info("仿真执行完成")
        logger.info("="*60)
        logger.info(f"执行时间: {duration}")
        logger.info(f"输出目录: {output_dir}")
        
        if results:
            logger.info("生成的数据文件:")
            for category, files in results.items():
                logger.info(f"  {category}:")
                for file_path in files:
                    logger.info(f"    - {file_path}")
        
        logger.info("仿真系统正常退出")
        return 0
        
    except KeyboardInterrupt:
        print("\n用户中断执行")
        return 1
    except Exception as e:
        if 'logger' in locals():
            logger.error(f"仿真执行失败: {e}")
            logger.exception("详细错误信息:")
        else:
            print(f"错误: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

"""
配置验证工具
提供JSON配置文件的验证和格式检查功能
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

from .logger import LoggerMixin


logger = logging.getLogger(__name__)


class ConfigValidator(LoggerMixin):
    """配置验证器"""
    
    def __init__(self):
        """初始化配置验证器"""
        self.validation_errors = []
        self.validation_warnings = []
    
    def validate_config_file(self, config_path: str) -> Tuple[bool, List[str], List[str]]:
        """
        验证配置文件
        
        Args:
            config_path: 配置文件路径
            
        Returns:
            (是否有效, 错误列表, 警告列表)
        """
        self.validation_errors.clear()
        self.validation_warnings.clear()
        
        try:
            # 检查文件存在性
            if not Path(config_path).exists():
                self.validation_errors.append(f"配置文件不存在: {config_path}")
                return False, self.validation_errors, self.validation_warnings
            
            # 加载JSON
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # 验证配置结构
            self._validate_config_structure(config_data)
            
            # 验证具体配置项
            self._validate_simulation_config(config_data.get('simulation', {}))
            self._validate_system_config(config_data.get('system', {}))
            self._validate_optical_targets(config_data.get('optical_targets', []))
            self._validate_optical_jammers(config_data.get('optical_jammers', []))
            self._validate_optical_recons(config_data.get('optical_recons', []))
            
            # 验证设备间的一致性
            self._validate_device_consistency(config_data)
            
            is_valid = len(self.validation_errors) == 0
            
            if is_valid:
                self.logger.info(f"配置文件验证通过: {config_path}")
            else:
                self.logger.error(f"配置文件验证失败: {config_path}")
                for error in self.validation_errors:
                    self.logger.error(f"  错误: {error}")
            
            if self.validation_warnings:
                for warning in self.validation_warnings:
                    self.logger.warning(f"  警告: {warning}")
            
            return is_valid, self.validation_errors, self.validation_warnings
            
        except json.JSONDecodeError as e:
            error_msg = f"JSON格式错误: {e}"
            self.validation_errors.append(error_msg)
            return False, self.validation_errors, self.validation_warnings
        
        except Exception as e:
            error_msg = f"配置验证异常: {e}"
            self.validation_errors.append(error_msg)
            return False, self.validation_errors, self.validation_warnings
    
    def _validate_config_structure(self, config: Dict[str, Any]):
        """验证配置结构"""
        required_sections = ['simulation', 'system']
        
        for section in required_sections:
            if section not in config:
                self.validation_errors.append(f"缺少必需的配置节: {section}")
        
        # 检查是否至少有一种设备配置
        device_sections = ['optical_targets', 'optical_jammers', 'optical_recons']
        has_devices = any(section in config and config[section] for section in device_sections)
        
        if not has_devices:
            self.validation_errors.append("至少需要配置一种光电设备")
    
    def _validate_simulation_config(self, sim_config: Dict[str, Any]):
        """验证仿真配置"""
        # 必需参数
        required_params = ['scenario_name', 'duration', 'time_step', 'data_count']
        for param in required_params:
            if param not in sim_config:
                self.validation_errors.append(f"仿真配置缺少必需参数: {param}")
        
        # 数值范围检查
        if 'duration' in sim_config:
            duration = sim_config['duration']
            if not isinstance(duration, (int, float)) or duration <= 0:
                self.validation_errors.append("仿真时长必须是正数")
        
        if 'time_step' in sim_config:
            time_step = sim_config['time_step']
            if not isinstance(time_step, (int, float)) or time_step <= 0:
                self.validation_errors.append("时间步长必须是正数")
        
        if 'data_count' in sim_config:
            data_count = sim_config['data_count']
            if not isinstance(data_count, int) or data_count <= 0:
                self.validation_errors.append("数据生成数量必须是正整数")
            elif data_count > 5000:
                self.validation_errors.append("数据生成数量不能超过5000")
        
        # 输出类型检查
        if 'output_types' in sim_config:
            valid_types = ['static_images', 'dynamic_images', 'parameters']
            output_types = sim_config['output_types']
            if not isinstance(output_types, list):
                self.validation_errors.append("输出类型必须是列表")
            else:
                for output_type in output_types:
                    if output_type not in valid_types:
                        self.validation_warnings.append(f"未知的输出类型: {output_type}")
    
    def _validate_system_config(self, sys_config: Dict[str, Any]):
        """验证系统配置"""
        # 线程数检查
        if 'max_threads' in sys_config:
            max_threads = sys_config['max_threads']
            if not isinstance(max_threads, int) or max_threads <= 0:
                self.validation_errors.append("最大线程数必须是正整数")
            elif max_threads > 32:
                self.validation_warnings.append("线程数过多可能影响性能")
        
        # 图像分辨率检查
        if 'image_resolution' in sys_config:
            resolution = sys_config['image_resolution']
            if not isinstance(resolution, list) or len(resolution) != 2:
                self.validation_errors.append("图像分辨率必须是包含两个数值的列表")
            else:
                width, height = resolution
                if not all(isinstance(x, int) and x > 0 for x in [width, height]):
                    self.validation_errors.append("图像分辨率必须是正整数")
                
                # 检查是否为640x480
                if (width, height) != (640, 480):
                    self.validation_warnings.append(f"建议使用640x480分辨率，当前: {width}x{height}")
        
        # 视频帧率检查
        if 'video_fps' in sys_config:
            fps = sys_config['video_fps']
            if not isinstance(fps, int) or fps <= 0:
                self.validation_errors.append("视频帧率必须是正整数")
            elif fps > 60:
                self.validation_warnings.append("视频帧率过高可能影响性能")
    
    def _validate_optical_targets(self, targets: List[Dict[str, Any]]):
        """验证光电目标配置"""
        for i, target in enumerate(targets):
            self._validate_device_common(target, f"光电目标 {i}")
            
            # 观察方向检查
            if 'observation_direction' not in target:
                self.validation_errors.append(f"光电目标 {i} 缺少观察方向配置")
            else:
                self._validate_direction(target['observation_direction'], f"光电目标 {i} 观察方向")
    
    def _validate_optical_jammers(self, jammers: List[Dict[str, Any]]):
        """验证光电干扰配置"""
        for i, jammer in enumerate(jammers):
            self._validate_device_common(jammer, f"光电干扰设备 {i}")
            
            # 干扰方向检查
            if 'jamming_direction' not in jammer:
                self.validation_errors.append(f"光电干扰设备 {i} 缺少干扰方向配置")
            else:
                self._validate_direction(jammer['jamming_direction'], f"光电干扰设备 {i} 干扰方向")
            
            # 干扰策略检查
            if 'jamming_strategy' not in jammer:
                self.validation_warnings.append(f"光电干扰设备 {i} 未指定干扰策略")
    
    def _validate_optical_recons(self, recons: List[Dict[str, Any]]):
        """验证光电侦察配置"""
        for i, recon in enumerate(recons):
            self._validate_device_common(recon, f"光电侦察设备 {i}")
            
            # 检测模式检查
            if 'detection_mode' not in recon:
                self.validation_warnings.append(f"光电侦察设备 {i} 未指定检测模式")
    
    def _validate_device_common(self, device: Dict[str, Any], device_name: str):
        """验证设备通用配置"""
        # 必需字段
        required_fields = ['model', 'position', 'performance_params', 'work_mode']
        for field in required_fields:
            if field not in device:
                self.validation_errors.append(f"{device_name} 缺少必需字段: {field}")
        
        # 位置信息检查
        if 'position' in device:
            self._validate_position(device['position'], device_name)
        
        # 性能参数检查
        if 'performance_params' in device:
            self._validate_performance_params(device['performance_params'], device_name)
    
    def _validate_position(self, position: Dict[str, Any], device_name: str):
        """验证位置信息"""
        required_coords = ['latitude', 'longitude', 'altitude']
        
        for coord in required_coords:
            if coord not in position:
                self.validation_errors.append(f"{device_name} 位置信息缺少: {coord}")
            else:
                value = position[coord]
                if not isinstance(value, (int, float)):
                    self.validation_errors.append(f"{device_name} {coord} 必须是数值")
                else:
                    # 范围检查
                    if coord == 'latitude' and not (-90 <= value <= 90):
                        self.validation_errors.append(f"{device_name} 纬度超出范围: {value}")
                    elif coord == 'longitude' and not (-180 <= value <= 180):
                        self.validation_errors.append(f"{device_name} 经度超出范围: {value}")
                    elif coord == 'altitude' and value < 0:
                        self.validation_warnings.append(f"{device_name} 海拔为负值: {value}")
    
    def _validate_direction(self, direction: Dict[str, Any], context: str):
        """验证方向信息"""
        if 'azimuth' not in direction:
            self.validation_errors.append(f"{context} 缺少方位角")
        else:
            azimuth = direction['azimuth']
            if not isinstance(azimuth, (int, float)):
                self.validation_errors.append(f"{context} 方位角必须是数值")
            elif not (0 <= azimuth <= 360):
                self.validation_warnings.append(f"{context} 方位角超出常规范围: {azimuth}")
        
        if 'elevation' not in direction:
            self.validation_errors.append(f"{context} 缺少俯仰角")
        else:
            elevation = direction['elevation']
            if not isinstance(elevation, (int, float)):
                self.validation_errors.append(f"{context} 俯仰角必须是数值")
            elif not (-90 <= elevation <= 90):
                self.validation_warnings.append(f"{context} 俯仰角超出常规范围: {elevation}")
    
    def _validate_performance_params(self, params: Dict[str, Any], device_name: str):
        """验证性能参数"""
        # 检查数值参数
        numeric_params = ['detection_range', 'resolution', 'field_of_view', 'power', 'sensitivity']
        
        for param in numeric_params:
            if param in params:
                value = params[param]
                if not isinstance(value, (int, float)) or value <= 0:
                    self.validation_errors.append(f"{device_name} {param} 必须是正数")
    
    def _validate_device_consistency(self, config: Dict[str, Any]):
        """验证设备间的一致性"""
        # 检查设备位置是否过于接近
        all_devices = []
        
        for device_type in ['optical_targets', 'optical_jammers', 'optical_recons']:
            if device_type in config:
                for i, device in enumerate(config[device_type]):
                    if 'position' in device:
                        all_devices.append({
                            'type': device_type,
                            'index': i,
                            'position': device['position'],
                            'model': device.get('model', 'unknown')
                        })
        
        # 检查设备间距离
        for i in range(len(all_devices)):
            for j in range(i + 1, len(all_devices)):
                device1 = all_devices[i]
                device2 = all_devices[j]
                
                distance = self._calculate_distance(
                    device1['position'], device2['position']
                )
                
                if distance < 100:  # 小于100米
                    self.validation_warnings.append(
                        f"设备 {device1['model']} 和 {device2['model']} 距离过近: {distance:.1f}m"
                    )
    
    def _calculate_distance(self, pos1: Dict[str, float], pos2: Dict[str, float]) -> float:
        """计算两个位置间的距离（简化计算）"""
        import math
        
        lat1, lon1, alt1 = pos1['latitude'], pos1['longitude'], pos1['altitude']
        lat2, lon2, alt2 = pos2['latitude'], pos2['longitude'], pos2['altitude']
        
        # 简化的距离计算（不考虑地球曲率）
        lat_diff = (lat2 - lat1) * 111000  # 纬度差转米
        lon_diff = (lon2 - lon1) * 111000 * math.cos(math.radians(lat1))  # 经度差转米
        alt_diff = alt2 - alt1
        
        return math.sqrt(lat_diff**2 + lon_diff**2 + alt_diff**2)


def validate_config_file(config_path: str) -> Tuple[bool, List[str], List[str]]:
    """
    验证配置文件的便捷函数
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        (是否有效, 错误列表, 警告列表)
    """
    validator = ConfigValidator()
    return validator.validate_config_file(config_path)
